# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# vscode
.vscode

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

expo-env.d.ts
# @end expo-cli

.env

.idea

android
pharmachainage-16466f8aefd5.json
client_secret_1079816468086-19kb7dll67nbr0ade0ovljv2lcgc2mi5.apps.googleusercontent.com.json
%ProgramData%/Microsoft/Windows/UUS/State/_active.uusver
