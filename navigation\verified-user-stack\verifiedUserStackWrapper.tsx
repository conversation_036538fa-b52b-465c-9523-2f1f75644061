import React from 'react';
import { authSelectors } from '../../services/authService';
import useGlobalStore from '../../services/globalState';
import VerifiedUserStack from './verifiedUserStack';

export default function VerifiedUserStackWrapper() {
    const firebaseUser = useGlobalStore(authSelectors.firebaseUser);
    console.log(firebaseUser)
    return <VerifiedUserStack firebaseUser={firebaseUser} />;
}
