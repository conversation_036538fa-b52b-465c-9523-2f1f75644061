import { Address } from "./address";
import { PlaceType } from "./constants";
import { Geometry } from "./geometry";

export interface BasicPlace {
    readonly geometry: Geometry;
    readonly placeId: string;
    readonly name: string;
}

export interface Place extends BasicPlace {
    readonly type: PlaceType;
    readonly tags?: ReadonlyArray<'HIDDEN'>;
    readonly googleMapsUri?: string;
    readonly addedAt?: Date;
}

export interface CustomPlace extends Place {
    readonly id: number;
    readonly address?: Address;
    readonly formattedAddress?: string;
    readonly note?: string;
    readonly createdAt: Date;
    readonly updatedAt?: Date;
}
