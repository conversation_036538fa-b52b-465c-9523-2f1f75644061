import { useState, useEffect, useRef } from 'react';
import Supercluster from 'supercluster';
import { PointFeature, AnyProps } from 'supercluster';
import MapView, { Camera } from 'react-native-maps';
import { CustomPlace, Place } from '../models/place';

const useMapClusters = (places: ReadonlyArray<Place | CustomPlace>, mapRef: React.RefObject<MapView>) => {

    const [clusters, setClusters] = useState<any[]>([]);

    const superclusterRef = useRef<Supercluster | null>(null);

    useEffect(() => {
        if (!superclusterRef.current) {
            superclusterRef.current = new Supercluster({
                radius: 50,
                maxZoom: 13,
            });
        }
    }, []);

    useEffect(() => {
        if (!superclusterRef.current) return;

        const points: Array<PointFeature<AnyProps>> = places.map((place) => ({
            type: "Feature",
            properties: {
                cluster: false,
                placeId: place.placeId
            },
            geometry: {
                type: 'Point',
                coordinates: [place.geometry.lng, place.geometry.lat],
            }
        }));

        superclusterRef.current.load(points);
        updateClusters();
    }, [places]);

    const updateClusters = async () => {
        if (!superclusterRef.current || !mapRef.current) return;

        const camera: Camera = await mapRef.current.getCamera();
        const zoom: number = camera.zoom ? Math.floor(camera.zoom) : 0;
        const { northEast, southWest } = await mapRef.current.getMapBoundaries();

        // Calculate the center and half-dimensions of the original bbox.
        const centerLon = (northEast.longitude + southWest.longitude) / 2;
        const centerLat = (northEast.latitude + southWest.latitude) / 2;
        const halfWidth = (northEast.longitude - southWest.longitude) / 2;
        const halfHeight = (northEast.latitude - southWest.latitude) / 2;

        // Determine the expansion factor.
        // For zoom levels below 14, keep the factor constant at 2.
        // For zoom levels 14 and above, increase the factor progressively:
        // e.g. zoom 14 -> factor = 3, zoom 15 -> factor = 4, etc.
        let expansionFactor;
        if (zoom < 15) {
            expansionFactor = 1;
        } else if (zoom >= 18) {
            expansionFactor = 1 + (zoom - 14) * 3; // Increase the factor more aggressively for zoom levels 18 and above
        } else {
            expansionFactor = 1 + (zoom - 14);
        }

        // Construct the expanded bounding box using the center and scaled half-dimensions.
        const expandedBbox: GeoJSON.BBox = [
            centerLon - halfWidth * expansionFactor,
            centerLat - halfHeight * expansionFactor,
            centerLon + halfWidth * expansionFactor,
            centerLat + halfHeight * expansionFactor,
        ];

        const clusters = superclusterRef.current.getClusters(expandedBbox, zoom);
        setClusters(clusters);
    };

    return { clusters, updateClusters };
};

export default useMapClusters;