import * as React from 'react';
import { Linking, StyleSheet } from "react-native";
import { Button, Dialog, Portal, Text } from 'react-native-paper';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';
import { WHATSAPP_NUMBER } from '../utils/constants';

interface Props {
    readonly visible: boolean;
    readonly causeOfSubscriptionRequest?: "TRIAL_PERIOD_ENDED" | "SUBSCRIPTION_ENDED" | null
    readonly onSubscribe: () => void;
    readonly subscribeButtonIcon: string;
    readonly onDismiss: () => void;
}

const SubscriptionDialog = (props: Props) => {

    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);

    const titleText: string = (() => {
        if ("TRIAL_PERIOD_ENDED" === props.causeOfSubscriptionRequest) {
            return translationService.translate("TRIAL_PERIOD_ENDED_DIALOG_TITLE")
        } else {
            return translationService.translate("SUBSCRIPTION_ENDED_DIALOG_TITLE")
        }
    })();

    const contentText: string = (() => {
        if ("TRIAL_PERIOD_ENDED" === props.causeOfSubscriptionRequest) {
            return translationService.translate("TRIAL_PERIOD_ENDED_DIALOG_CONTENT")
        } else {
            return translationService.translate("SUBSCRIPTION_ENDED_DIALOG_CONTENT")
        }
    })();

    const whatsAppSubscribeMsg: string = (() => {
        if ("TRIAL_PERIOD_ENDED" === props.causeOfSubscriptionRequest) {
            return "Bonjour, je voudrais m'abonner."
        } else {
            return "Bonjour, je voudrais renouveler mon abonnement."
        }
    })();    

    const onPressSubscribe = () => {
        props.onSubscribe();
        props.onDismiss();
    };

    if (props.causeOfSubscriptionRequest === undefined || props.causeOfSubscriptionRequest === null) {
        return null;
    }

    return (
        <Portal>
            <Dialog visible={props.visible} onDismiss={props.onDismiss}>
                <Dialog.Icon icon={"alert-circle-outline"} size={40} />
                <Dialog.Title>{titleText}</Dialog.Title>
                <Dialog.Content>
                    <Text variant="bodyLarge">{contentText}</Text>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={props.onDismiss}>{translationService.translate("LATER")}</Button>
                    <Button icon={props.subscribeButtonIcon} mode="contained" onPress={onPressSubscribe}>{translationService.translate("SUBSCRIBE").toUpperCase()}</Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
});

export default SubscriptionDialog;