import React, { useContext } from 'react';
import { StyleSheet, View } from "react-native";
import { <PERSON><PERSON>, Card, MD3Theme, useTheme } from "react-native-paper";
import { Geometry } from "../models/geometry";
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';
import { generateLatLng } from "../utils/others";

interface Props {
    readonly latLng: Geometry;
    readonly onAddPharmacy: (latLng: Geometry) => void;
    readonly onAddChainage: (latLng: Geometry) => void;
    readonly onDismiss: () => void;
}

const SelectedCoordCard: React.FC<Props> = ({ latLng, onAddChainage, onAddPharmacy }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const planMarkerLabel: string = translationService.translate("PLACE_CARD_PLAN_MARKER");
    const addPharmayLabel: string = translationService.translate("PLACE_CARD_ADD_PHARMACY");
    const addChainageLabel: string = translationService.translate("PLACE_CARD_ADD_CHAINAGE");

    const theme: MD3Theme = useTheme();

    const addPharmacy = () => {
        onAddPharmacy(latLng);
    };

    const addChainage = () => {
        onAddChainage(latLng);
    };

    return (
        <Card>
            <Card.Title
                title={planMarkerLabel}
                titleVariant="titleLarge"
                subtitle={generateLatLng(latLng.lat, latLng.lng)}
                subtitleVariant="bodyMedium"
                subtitleStyle={{
                    color: theme.colors.secondary
                }}
            />
            <Card.Actions>
                <View style={styles.cardActions}>
                    <Button testID="cardAddPharmacyButton" mode="outlined" onPress={addPharmacy}>
                        {addPharmayLabel}
                    </Button>
                    <Button testID="cardAddChainageButton" mode="contained" onPress={addChainage}>
                        {addChainageLabel}
                    </Button>
                </View>
            </Card.Actions>
        </Card>
    );
};

const styles = StyleSheet.create({
    cardActions: {
        flex: 1,
        flexDirection: "column",
        gap: 8
    }
});

export default SelectedCoordCard;
