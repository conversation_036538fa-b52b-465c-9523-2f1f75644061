type MainUserStackParamList = {
    Tabs: NavigatorScreenParams<BottomUserTabsStackParamList>;
    SavedPlaceDetails: {
        placeId: string;
    };
    SavedPlaceDetailsEdit: {
        placeId: string;
        fieldName: FIELD_NAME;
    };
    SavedPlaceAdd: {
        geometry: Geometry;
        placeType: PlaceType;
    };
    UserPersonalInfo: {
        
    };
    UserPersonalInfoEdit: {
        fieldName: string,
        fieldLabel: string,
        userDetails: UserInfo
    };
    Subscription: undefined;
    AuthSignUpForm: undefined;
};