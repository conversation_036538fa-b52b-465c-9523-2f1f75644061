import { Subscription } from "../services/api/models/subscription";

export interface UserInfo {
    readonly id: number;
    readonly uid: string;
    readonly uuid: string;
    readonly firstName?: string;
    readonly lastName?: string;
    readonly email: string;
    readonly phoneNumber?: string;
    readonly whatsappNumber?: string;
    readonly createdAt: string;
    readonly updatedAt?: string;
    readonly timezone?: string;
    readonly trialStartDate?: string;
    readonly trialPeriodInDays?: number;
    readonly subscriptions: ReadonlyArray<Subscription>
}