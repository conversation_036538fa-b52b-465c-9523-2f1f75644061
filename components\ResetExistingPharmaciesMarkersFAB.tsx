import * as React from 'react';
import { StyleSheet } from "react-native";
import { FAB } from 'react-native-paper';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';

interface Props {
    readonly visible: boolean;
    readonly onPress: () => void;
}

const ResetExistingPharmaciesMarkersFAB = (props: Props) => {

    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);

    return (
        <FAB
            visible={props.visible}
            icon="backup-restore"
            mode="elevated"
            variant='surface'
            color='red'
            style={[styles.fab]}
            onPress={props.onPress}
        />
    );
};

const styles = StyleSheet.create({
    fab: {
        position: 'absolute',
        bottom: 162
    }
});

export default ResetExistingPharmaciesMarkersFAB;