import * as React from 'react';
import { StyleSheet } from "react-native";
import { Button, Dialog, Portal, Text } from 'react-native-paper';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';

interface Props {
    readonly visible: boolean;
    readonly icon?: string;
    readonly title: string;
    readonly content: string;
    readonly onOk: () => void;
    readonly onDismiss: () => void;
}

const BasicInfoDialog = (props: Props) => {

    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);

    const onPressOk = () => {
        props.onDismiss();
        props.onOk();
    };

    return (
        <Portal>
            <Dialog visible={props.visible} onDismiss={props.onDismiss}>
                {props.icon && (<Dialog.Icon icon={props.icon} size={40} />)}
                <Dialog.Title>{props.title}</Dialog.Title>
                <Dialog.Content>
                    <Text variant="bodyLarge">{props.content}</Text>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={onPressOk}>{translationService.translate("OK")}</Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
});


export default BasicInfoDialog;