// Mock environment variables
process.env.EXPO_PUBLIC_API_URL = 'http://test-api.example.com';

// Mock API client and service
jest.mock('./services/api/apiHttpClient', () => ({
  apiHttpClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn()
  }
}));

jest.mock('./services/api/apiService', () => ({
  ApiService: jest.fn().mockImplementation(() => ({
    updateUser: jest.fn(),
    registerUser: jest.fn(),
    searchPharmacies: jest.fn(),
    getPharmacies: jest.fn(),
    getSearchSuggestions: jest.fn(),
    getPlaceDetailsByPlaceId: jest.fn(),
    deleteUser: jest.fn(),
    getUserDetails: jest.fn()
  }))
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock')
);

// Mock firebase
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(),
  initializeAuth: jest.fn(),
  onAuthStateChanged: jest.fn(),
  signOut: jest.fn()
}));

// Mock expo-localization
jest.mock('expo-localization', () => ({
  getCalendars: jest.fn(() => [{
    timeZone: 'Europe/Paris'
  }]),
  Calendar: {}
}));



