import { MarkerWithCircleStyle } from "./markerStyle";

export enum PlaceType {
  EXISTING_PHARMACY = "EXISTING_PHARMACY",
  PHARMACY = "PHARMACY",
  CHAINAGE = "CHAINAGE",
  SEARCH_RESULT = "SEARCH_RESULT"
}

export interface PlaceTypeParams {
  readonly tag: string;
  readonly label: string;
  readonly style: MarkerWithCircleStyle;
}

export const hiddenStyle = {
  color: "#ff0505",
  fillColor: "rgba(255,0,0,0.25)",
  strokeColor: "#ff0505"
}

export const placeTypeConf = {
  [PlaceType.EXISTING_PHARMACY]: {
    tag: "Pharmacie existante",
    label: "Pharmacie existante",
    style: {
      color: "#ff0505",
      fillColor: "rgba(255,0,0,0.25)",
      strokeColor: "#ff0505"
    },
    hiddenStyle: {
      color: "blue"
    }
  },
  [PlaceType.PHARMACY]: {
    tag: "Pharmacie ajoutée",
    label: "Pharmacie ajoutée",
    style: {
      color: "#ee7600",
      fillColor: "rgba(248,106,30,0.2)",
      strokeColor: "#ff8129"
    }
  },
  [PlaceType.CHAINAGE]: {
    tag: "Chaînage",
    label: "Chaînage",
    style: {
      color: "#34a91a",
      fillColor: "rgba(34,189,15,0.2)",
      strokeColor: "#38fa0d"
    }
  },
  [PlaceType.SEARCH_RESULT]: {
    tag: "Résultat de recherche",
    label: "Résultat de recherche",
    style: {
      color: "#4DA6FF", // Light Sky Blue - More visible in dark mode
      fillColor: "rgba(77, 166, 255, 0.4)", // Semi-transparent light blue
      strokeColor: "#FFFFFF" // White stroke for clear contrast
    }
  }
};
