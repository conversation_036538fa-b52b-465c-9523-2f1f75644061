import { StackScreenProps } from '@react-navigation/stack';
import { Auth, getAuth, signInWithCustomToken, UserCredential } from 'firebase/auth';
import { useFormik } from 'formik';
import React, { useContext, useState } from 'react';
import { Linking, Platform, ScrollView, StyleSheet, View } from 'react-native';
import { Button, HelperText, Text, TextInput } from "react-native-paper";
import * as Yup from 'yup';
import BasicInfoDialog from '../../components/BasicInfoDialog';
import { ApiService } from '../../services/api/apiService';
import { ApiError } from '../../services/api/models/apiError';
import { UserDetails } from '../../services/api/models/userDetails';
import { UserRegistrationRequest } from '../../services/api/models/userRegistrationRequest';
import useGlobalStore from '../../services/globalState';
import { apiServiceContext, formsServiceContext, translationServiceContext } from '../../services/provider';
import { TranslationService } from '../../services/translationService';
import { SignUpFormValues, YupFormsService } from '../../services/yupFormsService';

const PRIVACY_POLICY_PAGE_URL: string = "https://www.pharmachainage.com/privacy-policy/";
const TERMS_OF_SERVICE_PAGE_URL: string = "https://www.pharmachainage.com/terms-of-service/";

const SignUpScreen: React.FC<StackScreenProps<any>> = ({ navigation }) => {

    const apiService: ApiService = useContext<ApiService>(apiServiceContext);
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const formsService: YupFormsService = useContext<YupFormsService>(formsServiceContext);

    const auth: Auth = getAuth();

    const setFirebaseUser = useGlobalStore((state) => state.setFirebaseUser);

    const yupSignUpForm: Yup.ObjectSchema<SignUpFormValues> = formsService.getSignUpForm();

    const signUpInitialValues: SignUpFormValues = {
        name: '',
        whatsappNumber: undefined,
        email: '',
        password: ''
    };

    const onSuccessfulSignUp = async (userDetails: UserDetails) => {
        if (userDetails.customToken) {
            await signInWithCustomToken(auth, userDetails.customToken)
                .then((userCredential: UserCredential) => {
                    setFirebaseUser(userCredential.user);
                });
        }
    };

    const formik = useFormik({
        initialValues: signUpInitialValues,
        validationSchema: yupSignUpForm,
        onSubmit: async (values, formikActions) => {
            const userRegistrationRequest: UserRegistrationRequest = {
                email: values.email.toLowerCase(),
                firstName: values.name,
                whatsappNumber: values.whatsappNumber,
                password: values.password,
                platform: Platform.OS === 'android' ? 'ANDROID' : 'IOS'
            };
            const response = await apiService.registerUser(userRegistrationRequest);
            if (409 === response.status) {
                return response.json()
                    .then((apiError: ApiError) => {
                        if ("EMAIL_ALREADY_EXISTS" === apiError.code) {
                            formikActions.setFieldError('email', translationService.translate("EMAIL_ALREADY_EXISTS"));
                        }
                    });
            } else if (422 === response.status) {
                return response.json()
                    .then((apiError: ApiError) => {
                        if ("EMAIL_INVALID" === apiError.code) {
                            formikActions.setFieldError('email', translationService.translate("EMAIL_INVALID"));
                        }
                    });
            } else if (response.ok) {
                const userDetails: UserDetails = await response.json();
                await onSuccessfulSignUp(userDetails);
            } else {
                console.error("Something went wrong, cannot create the user.");
            }
            formikActions.setSubmitting(false);
        }
    });

    const getIsFieldRequired = (fieldName: keyof SignUpFormValues): boolean => {
        const fieldSchema = yupSignUpForm.describe().fields[fieldName];
        return fieldSchema.tests.some((test: any) => test.name === 'required');
    };

    const renderInputLabel = (label: string, isRequired: boolean): string => {
        return `${label} ${isRequired ? '' : `(${translationService.translate("FORM_OPTIONAL").toLocaleLowerCase()})`}`;
    };

    const isFieldInvalid = (fieldName: keyof SignUpFormValues): boolean => {
        return Boolean(formik.touched[fieldName] && formik.errors[fieldName]);
    };

    const [passwordVisible, setPasswordVisible] = useState<boolean>(false); // State for toggling visibility

    const [showDialog, setShowDialog] = useState(false);

    return (
        <ScrollView>
            <View style={styles.formContainer}>
                <BasicInfoDialog
                    visible={showDialog}
                    icon="email-alert"
                    title="Vérifiez votre email !"
                    content="Votre compte a bien été créé ! Consultez votre boîte de réception pour l’activer. Si vous ne voyez pas l’email, vérifiez vos spams ou contactez-nous."
                    onDismiss={() => {
                        setShowDialog(false)
                    }}
                    onOk={() => {
                        navigation.navigate("EmailVerificationScreen");
                    }}
                />
                <View style={styles.inputsContainer}>
                    <View>
                        <TextInput
                            onChangeText={formik.handleChange('name')}
                            onBlur={formik.handleBlur('name')}
                            disabled={formik.isSubmitting}
                            value={formik.values.name}
                            autoCapitalize="words"
                            keyboardType="default"
                            autoCorrect={false}
                            error={isFieldInvalid('name')}
                            mode="outlined"
                            label={renderInputLabel(translationService.translate("SIGN_UP_FORM_NAME"), getIsFieldRequired('name'))}
                        />
                        {isFieldInvalid('name') ? (
                            <HelperText type="error">{formik.errors['name']}</HelperText>
                        ) : null}
                    </View>
                    <View>
                        <TextInput
                            onChangeText={formik.handleChange('email')}
                            onBlur={formik.handleBlur('email')}
                            autoCapitalize="none"
                            keyboardType="email-address"
                            autoCorrect={false}
                            disabled={formik.isSubmitting}
                            value={formik.values.email}
                            error={isFieldInvalid('email')}
                            mode="outlined"
                            label={renderInputLabel(translationService.translate("SHORT_EMAIL"), getIsFieldRequired('email'))}
                        />
                        {isFieldInvalid('email') ? (
                            <HelperText type="error">{formik.errors.email}</HelperText>
                        ) : null}
                    </View>
                    <View>
                        <TextInput
                            onChangeText={formik.handleChange('whatsappNumber')}
                            onBlur={formik.handleBlur('whatsappNumber')}
                            disabled={formik.isSubmitting}
                            value={formik.values.whatsappNumber || undefined}
                            error={isFieldInvalid('whatsappNumber')}
                            autoCapitalize="none"
                            keyboardType="phone-pad"
                            autoCorrect={false}
                            placeholder='212XXXXXXXXX'
                            mode="outlined"
                            label={renderInputLabel(translationService.translate("SIGN_UP_FORM_WHATSAPP_NUMBER"), getIsFieldRequired('whatsappNumber'))}
                        />
                        {isFieldInvalid('whatsappNumber') && (
                            <HelperText type="error">{formik.errors.whatsappNumber}</HelperText>
                        )}
                        <HelperText type="info" style={styles.helperText}>
                            {translationService.translate("SIGN_UP_FORM_WHATSAPP_NUMBER_HELPER_TEXT")}
                        </HelperText>
                    </View>
                    <View>
                        <TextInput
                            secureTextEntry={!passwordVisible}
                            right={
                                <TextInput.Icon
                                    icon={passwordVisible ? 'eye-off' : 'eye'}
                                    onPress={() => setPasswordVisible(!passwordVisible)}
                                />
                            }
                            onChangeText={formik.handleChange('password')}
                            onBlur={formik.handleBlur('password')}
                            disabled={formik.isSubmitting}
                            value={formik.values.password}
                            error={isFieldInvalid('password')}
                            mode="outlined"
                            label={renderInputLabel(translationService.translate("SIGN_UP_FORM_PASSWORD"), getIsFieldRequired('password'))}
                        />
                        {isFieldInvalid('password') ? (
                            <HelperText type="error">{formik.errors.password}</HelperText>
                        ) : null}
                    </View>
                </View>
                <View style={styles.signUpWrapper}>
                    <Button
                        testID="authSignInButton"
                        mode="contained"
                        onPress={() => {
                            formik.handleSubmit()
                        }}
                        loading={formik.isSubmitting}
                    >
                        {translationService.translate("SIGN_UP_FORM_BUTTON_TEXT").toLocaleUpperCase()}
                    </Button>
                    <View>
                        <View style={styles.termsContainer}>
                            <Text variant="bodyMedium" style={[styles.termsText, styles.helperText]}>
                                En vous inscrivant, vous acceptez nos{' '}
                                <Text style={styles.externalLink} onPress={() => Linking.openURL(TERMS_OF_SERVICE_PAGE_URL)}>
                                    Conditions d'utilisation
                                </Text>
                                {' '}et notre{' '}
                                <Text style={styles.externalLink} onPress={() => Linking.openURL(PRIVACY_POLICY_PAGE_URL)}>
                                    Politique de confidentialité
                                </Text>.
                            </Text>
                        </View>
                    </View>
                </View>
            </View>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    signUpWrapper: {
        gap: 8
    },
    externalLink: {
        textDecorationLine: 'underline',
        color: '#4682B4'
    },
    textAlignCenter: {
        textAlign: 'center'
    },
    formContainer: {
        flexDirection: 'column',
        gap: 16,
        padding: 16,
    },
    inputsContainer: {
        gap: 8
    },
    termsContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
        paddingHorizontal: 8
    },
    helperText: {
        color: '#888'
    },
    termsText: {
        flex: 1,
        flexWrap: 'wrap',
    }
});

export default SignUpScreen;
