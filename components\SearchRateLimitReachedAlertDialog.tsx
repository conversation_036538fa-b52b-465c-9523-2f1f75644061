import * as React from 'react';
import { useContext } from 'react';
import { Linking, StyleSheet } from "react-native";
import { Button, Dialog, Portal, Text } from 'react-native-paper';
import { translationServiceContext, whatsappServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';
import { WhatsappService } from '../services/whatsappService';
import { WHATSAPP_HELP_MSG } from '../utils/constants';

interface Props {
    readonly visible: boolean;
    readonly subscribed: boolean;
    readonly subscribeButtonIcon: string;
    readonly limits: {
        readonly searchLimitPerDayForNonSubscribedUsers?: number,
        readonly searchLimitPerDayForSubscribedUsers?: number
    };
    readonly onDismiss: () => void;
    readonly onSubscribe: () => void;
};

const SearchRateLimitReachedAlertDialog = (props: Props) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const whatsappService: WhatsappService = useContext<WhatsappService>(whatsappServiceContext);

    const titleText: string = translationService.translate("QUERY_LIMIT_REACHED_DIALOG_TITLE");

    return (
        <Portal>
            <Dialog visible={props.visible} onDismiss={props.onDismiss}>
                <Dialog.Icon icon={"alert-circle-outline"} size={40} />
                <Dialog.Title>{titleText}</Dialog.Title>
                <Dialog.Content>
                    <Text variant="bodyLarge">
                        {props.subscribed ? (
                            <>
                                <Text>{`Vous avez utilisé vos `}</Text>
                                <Text style={styles.bold}>{`${props.limits.searchLimitPerDayForSubscribedUsers} recherches`}</Text>
                                <Text>{` pour aujourd'hui.\n`}</Text>
                            </>
                        ) : (
                            <>
                                <Text>{`Période d'essai : `}</Text>
                                <Text style={styles.bold}>{`${props.limits.searchLimitPerDayForNonSubscribedUsers} recherches/jour.\n`}</Text>
                                <Text>{`Abonné : `}</Text>
                                <Text style={styles.bold}>{`${props.limits.searchLimitPerDayForSubscribedUsers} recherches/jour.\n`}</Text>
                            </>
                        )}
                        <Text>{`Le compteur repart à `}<Text style={styles.bold}>{`00:00`}</Text>{`.`}</Text>
                    </Text>
                </Dialog.Content>
                <Dialog.Actions>
                    {props.subscribed ? (
                        <>
                            <Button onPress={props.onDismiss} uppercase>
                                {translationService.translate("OK")}
                            </Button>
                        </>
                    ) : (
                        <>
                            <Button onPress={props.onDismiss}>
                                {translationService.translate("LATER")}
                            </Button>
                            <Button icon={props.subscribeButtonIcon} mode="contained" uppercase onPress={props.onSubscribe}>
                                {translationService.translate("SUBSCRIBE").toUpperCase()}
                            </Button>
                        </>
                    )}
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    bold: {
        fontWeight: 'bold'
    }
});

export default SearchRateLimitReachedAlertDialog;
