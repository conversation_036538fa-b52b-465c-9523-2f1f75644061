import React from 'react';
import { StyleSheet, View } from "react-native";
import { IconButton, Text } from "react-native-paper";

interface Props {
    readonly label: string;
    readonly value?: string;
    readonly editDisabled?: boolean;
    readonly onEditPress?: () => void;
}

const ReadonlyInfo: React.FC<Props> = ({ label, onEditPress, editDisabled, value }) => {
    return (
        <View style={styles.container}>
            <View style={styles.dataContainer}>
                <Text variant="titleMedium">{label}</Text>
                <Text variant="bodyLarge" selectable={true}>{value ? value : "--"}</Text>
            </View>
            {onEditPress && (
                <View style={styles.actionContainer}>
                    <IconButton icon="pencil" onPress={onEditPress} disabled={editDisabled} />
                </View>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'wrap'
    },
    dataContainer: {
        flex: 1,
        gap: 6
    },
    actionContainer: {
        flex: 0,
        alignItems: 'center',
        justifyContent: 'center'
    }
});

export default ReadonlyInfo;
