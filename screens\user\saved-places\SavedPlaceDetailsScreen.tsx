import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { format } from 'date-fns';
import { fr } from "date-fns/locale";
import _ from "lodash";
import React, { useContext, useEffect, useState } from "react";
import { Linking, ScrollView, StyleSheet, View } from 'react-native';
import { Divider, ProgressBar } from "react-native-paper";
import confirmationDialog from "../../../components/ConfirmationDialog";
import ReadonlyInfo from "../../../components/ReadonlyInfo";
import SavedPlaceDetailsHeaderRight from "../../../components/SavedPlaceDetailsHeaderRigth";
import { PlaceType } from "../../../models/constants";
import { FIELD_NAME } from "../../../models/field";
import { Geometry } from "../../../models/geometry";
import { CustomPlace } from '../../../models/place';
import { ApiService } from "../../../services/api/apiService";
import { authSelectors } from "../../../services/authService";
import useGlobalStore from '../../../services/globalState';
import { apiServiceContext, translationServiceContext } from "../../../services/provider";
import { TranslationService } from "../../../services/translationService";
import { generateGoogleMapsURL, generateLatLng } from "../../../utils/others";

const formatDate = (date: Date): string => {
    return format(date, 'dd/MM/yyyy HH:mm', { locale: fr });
}

type SavedPlaceDetailsScreenProps = NativeStackScreenProps<MainUserStackParamList, 'SavedPlaceDetails'>;

const SavedPlaceDetailsScreen: React.FC<SavedPlaceDetailsScreenProps> = ({ navigation, route }) => {

    const userUid: string = useGlobalStore(authSelectors.authenticatedUserUid);

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const apiService: ApiService = useContext<ApiService>(apiServiceContext);

    const findSavedPlace: (placeId: string) => CustomPlace | undefined = useGlobalStore((store) => store.findSavedPlace);
    const savedPlaces: ReadonlyArray<CustomPlace> = useGlobalStore((store) => store.savedPlaces);
    const deleteSavedPlace = useGlobalStore((store) => store.deleteSavedPlace);

    const [savedPlace, setSavedPlace] = useState<CustomPlace | undefined>(findSavedPlace(route.params.placeId));

    const [deleting, setDeleting] = useState<boolean>(false);

    const showSnackbar = useGlobalStore((store) => store.showSnackbar);

    const placeTypeLabel = savedPlace ? translationService.translate(savedPlace.type) : "";
    const fieldNameLabel = translationService.translate("FIELD_NAME");
    const fieldNoteLabel = translationService.translate("FIELD_NOTE");
    const addressLabel = translationService.translate("ADDRESS");
    const coordinatesLabel = translationService.translate("COORDINATES");
    const creationDateLabel = translationService.translate("CREATION_DATE");
    const updateDateLabel = translationService.translate("UPDATE_DATE");

    const onMapPress = (savedPlace: CustomPlace) => {
        // @ts-ignore
        navigation.navigate('Home', {
            geometry: {
                lat: savedPlace.geometry.lat,
                lng: savedPlace.geometry.lng
            } as Geometry,
            placeId: savedPlace.placeId
        });
    };

    const onDelete = (savedPlace: CustomPlace) => {
        confirmationDialog({
            title: translationService.translate("DELETION_TITLE"),
            description: translationService.translate("DELETION_DESCRIPTION"),
            primaryActionLabel: translationService.translate("DELETE"),
            onPrimaryAction: () => {
                setDeleting(true);
                apiService.deleteUserSavedPlace(userUid, savedPlace.placeId)
                    .then(() => {
                        deleteSavedPlace(savedPlace.placeId);
                        navigation.goBack();
                        showSnackbar(
                            _.cond([
                                [(pt: PlaceType) => pt === PlaceType.PHARMACY, () => translationService.translate("PHARMACY_DELETED_SUCCESS")],
                                [(pt: PlaceType) => pt === PlaceType.CHAINAGE, () => translationService.translate("CHAINAGE_DELETED_SUCCESS")],
                                [_.stubTrue, () => translationService.translate("PLACE_DELETED_SUCCESS")]
                            ])(savedPlace.type),
                            'success'
                        );
                    })
                    .catch((error) => {
                        console.error("Failed to delete saved place:", error);
                        showSnackbar(
                            _.cond([
                                [(pt: PlaceType) => pt === PlaceType.PHARMACY, () => translationService.translate("PHARMACY_DELETION_FAILED")],
                                [(pt: PlaceType) => pt === PlaceType.CHAINAGE, () => translationService.translate("CHAINAGE_DELETION_FAILED")],
                                [_.stubTrue, () => translationService.translate("PLACE_DELETION_FAILED")]
                            ])(savedPlace.type),
                            'error'
                        );
                    })
                    .finally(() => {
                        setDeleting(false);
                    });
            }
            ,
            cancelActionLabel: translationService.translate("CANCEL"),
            onCancel: () => {
            }
        });
    };

    const onGoogleMapsPress = (savedPlace: CustomPlace) => {
        const googleMapsURL: string = generateGoogleMapsURL(savedPlace.geometry.lat, savedPlace.geometry.lng);
        Linking.openURL(googleMapsURL);
    };

    const navigateToEditModal = (savedPlace: CustomPlace, fieldName: FIELD_NAME) => {
        navigation.navigate({
            name: "SavedPlaceDetailsEdit",
            params: {
                placeId: savedPlace.placeId,
                fieldName
            }
        });
    };

    useEffect(() => {
        if (savedPlace) {
            navigation.setOptions({
                title: savedPlace.name,
                headerRight: () => (
                    <SavedPlaceDetailsHeaderRight
                        onMapPress={() => onMapPress(savedPlace)}
                        onDeletePress={() => onDelete(savedPlace)}
                        onGoogleMapsPress={() => onGoogleMapsPress(savedPlace)}
                    />
                ),
            });
        }
    }, [navigation, userUid, savedPlace]);

    useEffect(() => {
        setSavedPlace(findSavedPlace(route.params.placeId));
    }, [savedPlaces, route.params.placeId]);

    const isExistingPharmacy: boolean = PlaceType.EXISTING_PHARMACY !== savedPlace?.type;

    return (
        <ScrollView>
            {savedPlace && (
                <View>
                    <ProgressBar visible={deleting} indeterminate />
                    <View style={styles.formContainer}>
                        <ReadonlyInfo
                            label={fieldNameLabel}
                            value={savedPlace.name}
                            editDisabled={deleting}
                            onEditPress={isExistingPharmacy ? (() => navigateToEditModal(savedPlace, FIELD_NAME.NAME)) : undefined}
                        />
                        <Divider />
                        <ReadonlyInfo label={translationService.translate("TYPE_LABEL")} value={placeTypeLabel} />
                        <Divider />
                        <ReadonlyInfo
                            label={addressLabel}
                            value={savedPlace.formattedAddress}
                        />
                        <Divider />
                        <ReadonlyInfo
                            label={coordinatesLabel}
                            value={generateLatLng(savedPlace.geometry.lat, savedPlace.geometry.lng)}
                        />
                        <Divider />
                        {isExistingPharmacy && (
                            <>
                                <ReadonlyInfo
                                    label={fieldNoteLabel}
                                    value={savedPlace.note}
                                    editDisabled={deleting}
                                    onEditPress={() => navigateToEditModal(savedPlace, FIELD_NAME.NOTE)}
                                />
                                <Divider />
                                <ReadonlyInfo
                                    label={creationDateLabel}
                                    value={formatDate(savedPlace.createdAt)}
                                />
                                <Divider />
                                <ReadonlyInfo
                                    label={updateDateLabel}
                                    value={savedPlace.updatedAt ? formatDate(savedPlace.updatedAt) : undefined}
                                />
                            </>
                        )}
                    </View>
                </View>
            )}
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    formContainer: {
        flexDirection: 'column',
        gap: 8,
        padding: 16,
    },
    descriptionView: {
        flex: 1
    },
    placeType: {
        fontWeight: 'bold'
    }
});

export default SavedPlaceDetailsScreen;
