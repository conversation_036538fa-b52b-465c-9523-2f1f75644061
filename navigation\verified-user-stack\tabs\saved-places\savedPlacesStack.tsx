import { createNativeStackNavigator as createStackNavigator } from '@react-navigation/native-stack';
import React, { useContext } from 'react';
import SavedPlacesScreen from '../../../../screens/user/saved-places/SavedPlacesScreen';
import { translationServiceContext } from '../../../../services/provider';
import { TranslationService } from '../../../../services/translationService';

const SavedPlacesStackNavigator = createStackNavigator<SavedPlacesStackParamList>();

function SavedPlacesStack() {
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const savedTabLabel: string = translationService.translate("TABS_SAVED");

    return (
        <SavedPlacesStackNavigator.Navigator>
            <SavedPlacesStackNavigator.Screen
                name="SavedPlaces"
                options={{
                    title: savedTabLabel
                }}
                component={SavedPlacesScreen}
            />
        </SavedPlacesStackNavigator.Navigator>
    )
};

export default SavedPlacesStack;
