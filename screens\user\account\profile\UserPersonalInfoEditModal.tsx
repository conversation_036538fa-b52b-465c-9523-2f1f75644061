import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { useFormik } from "formik";
import React, { useContext, useEffect, useState } from "react";
import { StyleSheet, View } from 'react-native';
import { HelperText, ProgressBar, TextInput } from "react-native-paper";
import * as Yup from 'yup';
import EditHeaderRight from "../../../../components/EditHeaderRigth";
import { ApiService } from "../../../../services/api/apiService";
import { BaseUser } from "../../../../services/api/models/userDetails";
import { UserUpdateRequest } from "../../../../services/api/models/userUpdateRequest";
import { authSelectors } from "../../../../services/authService";
import useGlobalStore from '../../../../services/globalState';
import { apiServiceContext, formsServiceContext, translationServiceContext } from "../../../../services/provider";
import { TranslationService } from "../../../../services/translationService";
import { UserPersonalInfoEditForm, YupFormsService } from "../../../../services/yupFormsService";

type UserPersonalInfoEditModalScreenProps = NativeStackScreenProps<MainUserStackParamList, 'UserPersonalInfoEdit'>;

const UserPersonalInfoEditModal: React.FC<UserPersonalInfoEditModalScreenProps> = ({ navigation, route }) => {

    const userUid: string = useGlobalStore(authSelectors.authenticatedUserUid);

    const { fieldName, fieldLabel, userDetails } = route.params;

    const formsService: YupFormsService = useContext<YupFormsService>(formsServiceContext);
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const apiService: ApiService = useContext<ApiService>(apiServiceContext);

    const updateUser = useGlobalStore((store) => store.updateUser);

    const [selectAllText, setSelectAllText] = useState<boolean>(true);

    const userEditForm: Yup.ObjectSchema<UserPersonalInfoEditForm> = formsService.getUserPersonalInfoEditForm();

    const userInitialValues: UserPersonalInfoEditForm = {
        name: userDetails.firstName,
        whatsappNumber: userDetails.whatsappNumber,
    };

    const formik = useFormik({
        initialValues: userInitialValues,
        validationSchema: userEditForm,
        onSubmit: async (values, formikActions) => {
            const userUpdateRequest: UserUpdateRequest = {
                firstName: values.name,
                whatsappNumber: values.whatsappNumber
            };
            const baseUser: BaseUser = await apiService.updateUser(userDetails.uid, userUpdateRequest).then((response) => response.json());
            updateUser({
                ...userDetails,
                firstName: baseUser.firstName,
                whatsappNumber: baseUser.whatsappNumber
            });
            formikActions.setSubmitting(false);
            navigation.goBack();
        }
    });

    const fieldValue: any = formik.values[fieldName];
    const fieldErrorMsg: string | undefined = formik.getFieldMeta(fieldName)?.error;

    useEffect(() => {
        navigation.setOptions({
            headerTitle: `${translationService.translate("USER_INFO_UPDATE_DIALOG")}`,
            headerBackButtonMenuEnabled: true,
            headerBackVisible: true,
            headerBackTitle: translationService.translate("BACK"),
            headerRight: () => <EditHeaderRight onSave={formik.submitForm} saving={formik.isSubmitting} />
        });
    }, [navigation, userUid, formik.isSubmitting, userDetails, fieldName, fieldValue]);


    return (
        <View>
            <ProgressBar visible={formik.isSubmitting} indeterminate />
            <View style={styles.formContainer}>
                <TextInput
                    label={fieldLabel}
                    value={fieldValue}
                    disabled={formik.isSubmitting}
                    autoFocus
                    mode="outlined"
                    error={!!fieldErrorMsg}
                    selection={selectAllText && fieldValue ? { start: 0, end: fieldValue.length } : undefined}
                    onBlur={() => setSelectAllText(false)}
                    onKeyPress={() => setSelectAllText(false)}
                    onTouchStart={() => setSelectAllText(false)}
                    onChangeText={nextValue => {
                        formik.setFieldValue(fieldName, nextValue);
                        setSelectAllText(false);
                    }}
                />
                <HelperText type="error" visible={!!fieldErrorMsg}>
                    {fieldErrorMsg}
                </HelperText>
            </View>
        </View>

    );
};

const styles = StyleSheet.create({
    formContainer: {
        flexDirection: 'column',
        gap: 8,
        padding: 16,
    }
});

export default UserPersonalInfoEditModal;
