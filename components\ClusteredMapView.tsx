import React, { forwardRef } from 'react';
import { StyleSheet, View } from "react-native";
import MapView, { LatLng, MapPressEvent, Marker, PROVIDER_GOOGLE, Region } from 'react-native-maps';
import { Surface, Text, useTheme } from 'react-native-paper';
import { CustomPlace, Place } from '../models/place';
import PlaceMarker from './PlaceMarker';
import { CIRCLE_DIAMETER_IN_METERS } from '../utils/constants';
import { PlaceType, placeTypeConf } from '../models/constants';
import Supercluster, { AnyProps } from 'supercluster';

interface Props {
    readonly style: any;
    readonly initialRegion: Region;
    readonly places: ReadonlyArray<Place | CustomPlace>;
    readonly selectedPlace?: Place | CustomPlace;
    readonly clusters: Array<Supercluster.ClusterFeature<AnyProps>>;
    readonly showUserLocation: boolean;
    readonly addingMarker: boolean;
    readonly onClusterMarkerPress: (clusterMarkerCoordinates: LatLng) => void;
    readonly onMarkerPress: (place: Place | CustomPlace) => void;
    readonly onMapLoaded: () => void;
    readonly onRegionChangeComplete: (newRegion: Region, details: any) => void;
    readonly onPress: (event: MapPressEvent) => void;
}

const ClusteredMapView = forwardRef<MapView, Props>(({
    style,
    initialRegion,
    places,
    clusters,
    selectedPlace,
    showUserLocation,
    addingMarker,
    onMarkerPress,
    onClusterMarkerPress,
    onMapLoaded,
    onRegionChangeComplete,
    onPress
}, ref) => {

    const theme = useTheme();

    const renderCluster = (cluster: Supercluster.ClusterFeature<AnyProps>) => {
        const { id, geometry, properties } = cluster;
        const { point_count } = properties;
        const [longitude, latitude] = geometry.coordinates;

        return (
            <Marker
                key={`cluster-${id}`}
                coordinate={{ latitude, longitude }}
                onPress={() => onClusterMarkerPress({ latitude, longitude })}
            >
                <View style={styles.clusterContainer}>
                    <Text style={styles.clusterText}>{point_count}</Text>
                </View>
            </Marker>
        );
    };

    const renderPlaceMarker = (place: Place | CustomPlace) => {
        const { style } = placeTypeConf[place.type];
        return (
            <PlaceMarker
                key={`${place.placeId}-${place.geometry.lat},${place.geometry.lng}`}
                place={place}
                selected={selectedPlace?.placeId === place.placeId}
                showCircle={(!place.tags?.includes("HIDDEN") || PlaceType.SEARCH_RESULT === place.type)}
                markerColor={style.color}
                circleDiameter={CIRCLE_DIAMETER_IN_METERS}
                fillColor={style.fillColor}
                strokeColor={style.strokeColor}
                onPress={() => {
                    if (addingMarker) {
                        return;
                    }
                    onMarkerPress(place);
                }}
            />
        );
    };

    return (
        <MapView
            ref={ref}
            style={style}
            provider={PROVIDER_GOOGLE}
            mapType="hybrid"
            showsCompass={true}
            showsUserLocation={showUserLocation}
            showsMyLocationButton={false}
            rotateEnabled={true}
            toolbarEnabled={false}
            onCalloutPress={() => { }}
            initialRegion={initialRegion}
            onMapLoaded={onMapLoaded}
            onPress={onPress}
            onRegionChangeComplete={onRegionChangeComplete}
        >
            {clusters.map((cluster: Supercluster.ClusterFeature<AnyProps>) => {
                if (cluster.properties.cluster) {
                    return renderCluster(cluster);
                } else {
                    const place: Place | CustomPlace | undefined = places.find(currentPlace => currentPlace.placeId === cluster.properties.placeId);
                    if (!place) {
                        return null;
                    } else {
                        return renderPlaceMarker(place);
                    }
                }
            })}
        </MapView>
    );
});

const styles = StyleSheet.create({
    clusterContainer: {
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: 'rgba(0, 150, 136, 0.7)',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 2,
        borderColor: '#fff',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.8,
        shadowRadius: 2,
        elevation: 5,
    },
    clusterText: {
        color: '#fff',
        fontSize: 18,
        fontWeight: 'bold',
    },
});

export default ClusteredMapView;
