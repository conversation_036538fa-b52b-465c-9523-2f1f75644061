import { differenceInCalendarDays, differenceInHours, differenceInMinutes, differenceInSeconds, isWithinInterval } from "date-fns";
import { SubscriptionPeriod, TimeRemaining } from "../models/types";

export const calculateTimeRemaining = (endDate: Date, currentTime: Date): TimeRemaining => {
    const days: number = Math.max(0, differenceInCalendarDays(endDate, currentTime));
    const hours: number = Math.max(0, differenceInHours(endDate, currentTime));
    const minutes: number = Math.max(0, differenceInMinutes(endDate, currentTime));
    const seconds: number = Math.max(0, differenceInSeconds(endDate, currentTime));

    return { days, hours, minutes, seconds };
};

export function isSubscriptionActive(currentOrLatestPastSubscription: SubscriptionPeriod, currentTime: Date): boolean {
    return isWithinInterval(currentTime, { start: currentOrLatestPastSubscription.startDate, end: currentOrLatestPastSubscription.endDate });
};
