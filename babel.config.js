module.exports = function (api) {
    api.cache(true);
    return {
        presets: ['babel-preset-expo'],
        env: {
            production: {
                plugins: [
                    // Rewrites the import statements so that only the modules you use are imported instead of the whole library
                    'react-native-paper/babel'
                ]
            }
        },
        plugins: [
        ],
    };
};
