name: update
on:
  push:
    branches:
      - main

jobs:
  update:
    name: EAS Update
    runs-on: ubuntu-latest
    steps:
      - name: Check for EXPO_TOKEN
        run: |
          if [ -z "${{ secrets.EXPO_TOKEN }}" ]; then
            echo "You must provide an EXPO_TOKEN secret linked to this project's Expo account in this repo's secrets. Learn more: https://docs.expo.dev/eas-update/github-actions"
            exit 1
          fi

      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 18.x
          cache: npm

      - name: Setup EAS
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}

      - name: Install dependencies
        run: npm install

      - name: Generate .env file
        run: |
          echo "EXPO_PUBLIC_API_URL=${{ vars.API_URL }}" >> .env
          echo "GOOGLE_MAPS_ANDROID_API_KEY=${{ secrets.GOOGLE_MAPS_ANDROID_API_KEY }}" >> .env
          echo "GOOGLE_MAPS_IOS_API_KEY=${{ secrets.GOOGLE_MAPS_IOS_API_KEY }}" >> .env
          echo "EXPO_PUBLIC_FIREBASE_API_KEY=${{ vars.FIREBASE_API_KEY }}" >> .env
          echo "EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=${{ vars.FIREBASE_AUTH_DOMAIN }}" >> .env
          echo "EXPO_PUBLIC_FIREBASE_PROJECT_ID=${{ vars.FIREBASE_PROJECT_ID }}" >> .env
          echo "EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=${{ vars.FIREBASE_STORAGE_BUCKET }}" >> .env
          echo "EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${{ vars.FIREBASE_MESSAGING_SENDER_ID }}" >> .env
          echo "EXPO_PUBLIC_FIREBASE_APP_ID=${{ vars.FIREBASE_APP_ID }}" >> .env
          echo "EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID=${{ vars.FIREBASE_MEASUREMENT_ID }}" >> .env

      - name: Publish update
        run: eas update --auto
