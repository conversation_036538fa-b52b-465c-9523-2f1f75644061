import * as React from 'react';
import { StyleSheet } from "react-native";
import { Button, Dialog, Portal, Text } from 'react-native-paper';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';

interface Props {
    readonly visible: boolean;
    readonly onDelete: () => Promise<void>;
    readonly onDismiss: () => void;
}

const DeleteAccountDialog = (props: Props) => {

    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);
    
    const [deleting, setDeleting] = React.useState<boolean>(false);

    const handleDelete = () => {
        setDeleting(true);
        props.onDelete()
            .finally(() => {
                setDeleting(false);
            })
    };

    return (
        <Portal>
            <Dialog visible={props.visible} onDismiss={props.onDismiss}>
                <Dialog.Icon icon={"delete"} size={40} />
                <Dialog.Title>{translationService.translate("DELETE_ACCOUNT_DIALOG_TITLE")}</Dialog.Title>
                <Dialog.Content>
                    <Text variant="bodyLarge">{translationService.translate("DELETE_ACCOUNT_DIALOG_CONTENT")}</Text>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={props.onDismiss} disabled={deleting}>
                        {translationService.translate("CANCEL")}
                    </Button>
                    <Button onPress={handleDelete} loading={deleting}>
                        {translationService.translate("DELETE")}
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
});


export default DeleteAccountDialog;