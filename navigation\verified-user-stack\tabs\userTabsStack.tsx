import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { NavigationState, StackActions } from '@react-navigation/native';
import React, { useContext } from 'react';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { translationServiceContext } from "../../../services/provider";
import { TranslationService } from "../../../services/translationService";
import AccountStack from './account/accountStack';
import HomeStack from './home/<USER>';
import SavedPlacesStack from './saved-places/savedPlacesStack';

const UserTabNavigator = createBottomTabNavigator<UserTabsStackParamList>();

export default function UserTabsStack() {
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    return (
        <UserTabNavigator.Navigator
            screenOptions={{
                tabBarHideOnKeyboard: true
            }}
        >
            <UserTabNavigator.Screen
                name="ExploreTab"
                component={HomeStack}
                listeners={({ navigation, route }) => ({
                    tabPress: e => {
                        const state = navigation.getState() as NavigationState;
                        const exploreTabState = state.routes
                            .find((route) => route.name === 'ExploreTab')
                            ?.state as NavigationState | undefined;
                        // If the stack is deeper than index 0, pop to top
                        if (exploreTabState && exploreTabState.index > 0) {
                            navigation.dispatch(StackActions.popToTop());
                        }
                    }
                })}
                options={{
                    headerShown: false,
                    tabBarLabel: translationService.translate("TABS_EXPLORE"),
                    tabBarIcon: ({ color, size }) => (
                        <MaterialCommunityIcons name="navigation-variant" color={color} size={size} />
                    )
                }}
            />
            <UserTabNavigator.Screen
                name="SavedPlacesTab"
                component={SavedPlacesStack}
                options={{
                    headerShown: false,
                    tabBarLabel: translationService.translate("TABS_SAVED"),
                    tabBarIcon: ({ color, size }) => (
                        <MaterialCommunityIcons name="bookmark" color={color} size={size} />
                    )
                }}
            />
            <UserTabNavigator.Screen
                name="AccountTab"
                component={AccountStack}
                options={{
                    headerShown: false,
                    tabBarLabel: translationService.translate("TABS_ACCOUNT"),
                    tabBarIcon: ({ color, size }) => (
                        <MaterialCommunityIcons name="account-cog" color={color} size={size} />
                    )
                }}
            />
        </UserTabNavigator.Navigator>
    );
};
