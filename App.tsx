import 'react-native-gesture-handler';
// check https://github.com/expo/expo/issues/28618#issuecomment-2099225578
import 'react-native-reanimated';
// check https://github.com/vonovak/react-navigation-header-buttons/blob/master/INSTALL.md
// @ts-ignore
import Entypo from '@expo/vector-icons/Entypo';
import { DarkTheme as DTheme, DefaultTheme, NavigationContainer } from "@react-navigation/native";
import * as Font from 'expo-font';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { User } from 'firebase/auth';
import React, { useCallback, useContext, useEffect, useState } from 'react';
import { useColorScheme } from 'react-native';
import { MD3DarkTheme, MD3LightTheme, MD3Theme, PaperProvider, adaptNavigationTheme } from 'react-native-paper';
import { HeaderButtonsProvider } from 'react-navigation-header-buttons/HeaderButtonsProvider';
import AuthLoadingIndicator from './components/AuthLoadingIndicator';
import OperationSnackbars from './components/OperationSnackbars';
import PurchaseFlowDialogs from './components/purchase/PurchaseDialogs';
import AuthStack from "./navigation/auth/authStack";
import MainUserStack from './navigation/verified-user-stack/mainUserStack';
import { ApiService } from './services/api/apiService';
import { authSelectors, initializeAuthService } from "./services/authService";
import useGlobalStore from './services/globalState';
import { apiServiceContext, revenueCatServiceContext } from './services/provider';
import { RevenueCatService } from './services/revenueCatService';
import VerifiedUserStack from './navigation/verified-user-stack/verifiedUserStack';
import VerifiedUserStackWrapper from './navigation/verified-user-stack/verifiedUserStackWrapper';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

interface RootLayoutNavProps {
    onLayout: () => Promise<void>;
    user: User | null;
};

export default function RootLayout() {
    const revenueCatService: RevenueCatService = useContext<RevenueCatService>(revenueCatServiceContext);
    const apiService: ApiService = useContext<ApiService>(apiServiceContext);
    
    const firebaseUser: User | null = useGlobalStore(authSelectors.firebaseUser);
    const authenticating: boolean = useGlobalStore(authSelectors.authenticating);

    const [appIsReady, setAppIsReady] = useState<boolean>(false);

    // Initialize services
    useEffect(() => {
        async function initializeServices() {
            try {
                // First initialize RevenueCat
                await revenueCatService.initialize();
                // Then initialize auth service with consolidated listener
                const unsubscribeAuth = initializeAuthService(apiService, revenueCatService);
                return () => {
                    unsubscribeAuth();
                };
            } catch (error) {
                console.error("Error initializing services:", error);
            }
        }

        const cleanup = initializeServices();
        return () => {
            if (cleanup) {
                cleanup.then(unsubFn => unsubFn && unsubFn());
            }
        };
    }, []);

    useEffect(() => {
        async function prepare() {
            try {
                // Pre-load fonts, make any API calls you need to do here
                await Font.loadAsync(Entypo.font);
            } catch (e) {
                console.warn(e);
            } finally {
                setAppIsReady(true);
            }
        }

        prepare();
    }, []);

    const onLayoutRootView = useCallback(async () => {
        if (appIsReady && !authenticating) {
            // This tells the splash screen to hide immediately! If we call this after
            // `setAppIsReady`, then we may see a blank screen while the app is
            // loading its initial state and rendering its first pixels. So instead,
            // we hide the splash screen once we know the root view has already
            // performed layout.
            await SplashScreen.hideAsync();
        }
    }, [appIsReady, authenticating]);

    if (!appIsReady) {
        return null; // Keep the splash screen or a placeholder component visible
    }

    return <RootLayoutNav onLayout={onLayoutRootView} user={firebaseUser} />;
}

const customTheme = {
    roundness: 4
};

const customMD3DarkTheme = {
    ...MD3DarkTheme,
    ...customTheme,
    "colors": {
        "primary": "rgb(140, 218, 107)",
        "onPrimary": "rgb(14, 57, 0)",
        "primaryContainer": "rgb(24, 82, 0)",
        "onPrimaryContainer": "rgb(167, 247, 132)",
        "secondary": "rgb(188, 203, 176)",
        "onSecondary": "rgb(39, 52, 33)",
        "secondaryContainer": "rgb(61, 75, 54)",
        "onSecondaryContainer": "rgb(216, 231, 203)",
        "tertiary": "rgb(160, 207, 208)",
        "onTertiary": "rgb(0, 55, 56)",
        "tertiaryContainer": "rgb(30, 78, 79)",
        "onTertiaryContainer": "rgb(187, 235, 236)",
        "error": "rgb(255, 180, 171)",
        "onError": "rgb(105, 0, 5)",
        "errorContainer": "rgb(147, 0, 10)",
        "onErrorContainer": "rgb(255, 180, 171)",
        "background": "rgb(26, 28, 24)",
        "onBackground": "rgb(227, 227, 220)",
        "surface": "rgb(26, 28, 24)",
        "onSurface": "rgb(227, 227, 220)",
        "surfaceVariant": "rgb(67, 72, 62)",
        "onSurfaceVariant": "rgb(195, 200, 187)",
        "outline": "rgb(141, 146, 135)",
        "outlineVariant": "rgb(67, 72, 62)",
        "shadow": "rgb(0, 0, 0)",
        "scrim": "rgb(0, 0, 0)",
        "inverseSurface": "rgb(227, 227, 220)",
        "inverseOnSurface": "rgb(47, 49, 45)",
        "inversePrimary": "rgb(37, 109, 4)",
        "elevation": {
            "level0": "transparent",
            "level1": "rgb(32, 38, 28)",
            "level2": "rgb(35, 43, 31)",
            "level3": "rgb(39, 49, 33)",
            "level4": "rgb(40, 51, 34)",
            "level5": "rgb(42, 55, 36)"
        },
        "surfaceDisabled": "rgba(227, 227, 220, 0.12)",
        "onSurfaceDisabled": "rgba(227, 227, 220, 0.38)",
        "backdrop": "rgba(45, 50, 41, 0.4)"
    }
}

const customMD3LightTheme = {
    ...MD3LightTheme,
    ...customTheme,
    "colors": {
        "primary": "rgb(37, 109, 4)",
        "onPrimary": "rgb(255, 255, 255)",
        "primaryContainer": "rgb(167, 247, 132)",
        "onPrimaryContainer": "rgb(6, 33, 0)",
        "secondary": "rgb(85, 98, 76)",
        "onSecondary": "rgb(255, 255, 255)",
        "secondaryContainer": "rgb(216, 231, 203)",
        "onSecondaryContainer": "rgb(19, 31, 13)",
        "tertiary": "rgb(56, 102, 103)",
        "onTertiary": "rgb(255, 255, 255)",
        "tertiaryContainer": "rgb(187, 235, 236)",
        "onTertiaryContainer": "rgb(0, 32, 33)",
        "error": "rgb(186, 26, 26)",
        "onError": "rgb(255, 255, 255)",
        "errorContainer": "rgb(255, 218, 214)",
        "onErrorContainer": "rgb(65, 0, 2)",
        "background": "rgb(253, 253, 246)",
        "onBackground": "rgb(26, 28, 24)",
        "surface": "rgb(253, 253, 246)",
        "onSurface": "rgb(26, 28, 24)",
        "surfaceVariant": "rgb(223, 228, 215)",
        "onSurfaceVariant": "rgb(67, 72, 62)",
        "outline": "rgb(115, 121, 110)",
        "outlineVariant": "rgb(195, 200, 187)",
        "shadow": "rgb(0, 0, 0)",
        "scrim": "rgb(0, 0, 0)",
        "inverseSurface": "rgb(47, 49, 45)",
        "inverseOnSurface": "rgb(241, 241, 234)",
        "inversePrimary": "rgb(140, 218, 107)",
        "elevation": {
            "level0": "transparent",
            "level1": "rgb(242, 246, 234)",
            "level2": "rgb(236, 242, 227)",
            "level3": "rgb(229, 237, 219)",
            "level4": "rgb(227, 236, 217)",
            "level5": "rgb(223, 233, 212)"
        },
        "surfaceDisabled": "rgba(26, 28, 24, 0.12)",
        "onSurfaceDisabled": "rgba(26, 28, 24, 0.38)",
        "backdrop": "rgba(45, 50, 41, 0.4)"
    }
}

const { LightTheme, DarkTheme } = adaptNavigationTheme({
    reactNavigationLight: DefaultTheme,
    reactNavigationDark: DTheme,
    materialLight: customMD3LightTheme,
    materialDark: customMD3DarkTheme
});

const stackType = 'native';

function RootLayoutNav({ onLayout, user }: RootLayoutNavProps): JSX.Element {
    // TODO fix dark light mode
    const colorScheme = useColorScheme();
    const paperTheme: MD3Theme = colorScheme === 'dark' ? customMD3DarkTheme : customMD3LightTheme;
    const navigationContainerTheme = colorScheme === 'dark' ? DarkTheme : LightTheme;

    const authenticating: boolean = useGlobalStore(state => state.authenticating);

    return (
        <PaperProvider theme={paperTheme}>
            <StatusBar style="auto" />
            <NavigationContainer theme={navigationContainerTheme} onReady={onLayout}>
                <HeaderButtonsProvider stackType={stackType} spaceAboveMenu={100}>
                    {user ? (<VerifiedUserStackWrapper />) : (<AuthStack />)}
                </HeaderButtonsProvider>
                <OperationSnackbars />
                <PurchaseFlowDialogs />
                {authenticating && <AuthLoadingIndicator />}
            </NavigationContainer>
        </PaperProvider>
    );
}
