import AsyncStorage from "@react-native-async-storage/async-storage";
import { initializeApp } from "firebase/app";
// @ts-ignore
import { getReactNativePersistence, initializeAuth } from "firebase/auth";
import { requireNonNull } from "../utils/others";

const firebaseConfig = {
    apiKey: requireNon<PERSON>ull(process.env.EXPO_PUBLIC_FIREBASE_API_KEY),
    authDomain: requireNon<PERSON>ull(process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN),
    projectId: requireNonNull(process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID),
    storageBucket: requireNon<PERSON>ull(process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET),
    messagingSenderId: requireNonNull(process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID),
    appId: requireN<PERSON><PERSON>ull(process.env.EXPO_PUBLIC_FIREBASE_APP_ID),
    measurementId: require<PERSON><PERSON><PERSON><PERSON>(process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID)
};

const app = initializeApp(firebaseConfig);

const auth = initializeAuth(app, {
    persistence: getReactNativePersistence(AsyncStorage)
});

export { auth };

