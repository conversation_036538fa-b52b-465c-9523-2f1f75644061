import { StackScreenProps } from '@react-navigation/stack';
import React, { useContext } from 'react';
import { Image, StyleSheet, View } from 'react-native';
import { Button, Text } from "react-native-paper";
import { translationServiceContext } from '../../services/provider';
import { TranslationService } from '../../services/translationService';

const AuthScreen: React.FC<StackScreenProps<any>> = ({ navigation }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    return (
        <View style={styles.container}>
            <View style={styles.headerContainer}>
                <Image source={require('../../assets/images/icon-reverse.png')} style={styles.logo} />
                <Text variant="headlineLarge">{translationService.translate("APP_TITLE")}</Text>
                <Text variant="titleMedium">{translationService.translate("APP_DESCRIPTION")}</Text>
            </View>
            <View style={styles.formContainer}>
                <Button icon="crosshairs-gps" mode="contained" onPress={() => navigation.navigate("AuthForm")}>
                    {"Y ALLER !"}
                </Button>
            </View>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        gap: 40,
        alignItems: 'center',
        justifyContent: 'center',
    },
    headerContainer: {
        gap: 8,
        alignItems: 'center',
        justifyContent: 'center',
    },
    logo: {
        width: 140,
        height: 140
    },
    formContainer: {
        gap: 14
    },
    inputsContainer: {
        gap: 2, width: 250
    },
    buttons: {
        flex: 1,
    },
    button: {
        marginTop: 10
    }
});

export default AuthScreen;
