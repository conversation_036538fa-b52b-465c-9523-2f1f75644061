import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Button, Dialog, Text, Portal, useTheme, Divider } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { translationServiceContext } from "../services/provider";
import { useContext } from 'react';
import { TranslationService } from '../services/translationService';
import AccountBenefitsList from './AccountBenefitsList';

type SignUpPromptDialogProps = {
    visible: boolean;
    onDismiss: () => void;
    featureName?: string;
};

const SignUpPromptDialog: React.FC<SignUpPromptDialogProps> = ({ visible, onDismiss, featureName }) => {
    const theme = useTheme();
    const navigation = useNavigation<NativeStackNavigationProp<any>>();
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const handleSignUp = () => {
        onDismiss();
        navigation.navigate("AuthSignUpForm");
    };

    const handleGuestContinue = () => {
        onDismiss();
    };

    return (
        <Portal>
            <Dialog visible={visible} onDismiss={onDismiss} style={styles.dialog}>
                <Dialog.Icon 
                    icon="account-star" 
                    size={56} 
                    color={theme.colors.primary} 
                />
                <Dialog.Title style={styles.title}>
                    Créer un compte gratuit ?
                </Dialog.Title>
                <Dialog.Content>
                    <Text variant="bodyLarge" style={[styles.description, { color: theme.colors.onSurface }]}>
                        Profitez de toutes les fonctionnalités de PharmaChainage
                    </Text>
                    
                    <View style={styles.benefitsContainer}>
                        <AccountBenefitsList
                            variant="signup"
                            titleStyle={styles.benefitText}
                            itemStyle={styles.benefitItem}
                        />
                    </View>

                    <Divider style={styles.divider} />
                    
                    <Text variant="bodyMedium" style={[styles.guestNote, { color: theme.colors.onSurfaceVariant }]}>
                        Vous pouvez aussi continuer en mode invité avec des fonctionnalités limitées.
                    </Text>
                </Dialog.Content>
                <Dialog.Actions style={styles.actions}>
                    <Button
                        onPress={handleGuestContinue}
                        textColor={theme.colors.onSurfaceVariant}
                        style={styles.guestButton}
                    >
                        Mode invité
                    </Button>
                    <Button
                        mode="contained"
                        onPress={handleSignUp}
                        icon="account-plus"
                        style={styles.signUpButton}
                    >
                        {translationService.translate("SIGN_UP_LOGIN_BUTTON")}
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    dialog: {
        borderRadius: 16,
    },
    title: {
        textAlign: 'center',
        fontSize: 20,
        fontWeight: '600',
        marginBottom: 8,
    },
    description: {
        textAlign: 'center',
        marginBottom: 16,
        fontSize: 16,
    },
    benefitsContainer: {
        marginVertical: 8,
    },
    benefitItem: {
        paddingVertical: 4,
        paddingHorizontal: 0,
    },
    benefitText: {
        fontSize: 15,
        fontWeight: '500',
    },
    divider: {
        marginVertical: 16,
    },
    guestNote: {
        textAlign: 'center',
        fontStyle: 'italic',
        marginTop: 8,
    },
    actions: {
        justifyContent: 'space-between',
        paddingHorizontal: 8,
        paddingTop: 16,
    },
    guestButton: {
        flex: 0,
        minWidth: 100,
    },
    signUpButton: {
        flex: 0,
        minWidth: 140,
    },
});

export default SignUpPromptDialog;
