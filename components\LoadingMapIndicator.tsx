import * as React from 'react';
import { StyleSheet, View } from "react-native";
import { ActivityIndicator, Text, useTheme } from 'react-native-paper';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';

interface Props {
}

const LoadingMapIndicator = (props: Props) => {

    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);

    const theme = useTheme(); // Access the current theme

    return (
        <View style={[
            styles.loadingOverlay,
            { backgroundColor: theme.colors.background }, // Use the theme's background color
        ]}>
            <ActivityIndicator animating={true} size="large" />
            <Text variant="bodyMedium" style={styles.loadingText}>Préparation de votre carte... Un instant !</Text>
        </View>
    );
};

const styles = StyleSheet.create({
    loadingOverlay: {
        ...StyleSheet.absoluteFillObject,
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 9999,
    },
    loadingText: {
        marginTop: 12
    }
});


export default LoadingMapIndicator;