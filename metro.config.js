const { getDefaultConfig } = require('@expo/metro-config');

const config = getDefaultConfig(__dirname);

module.exports = (async () => {
   const {
      resolver: { sourceExts },
   } = config;

   return {
      ...config,
      resolver: {
         ...config.resolver,
         // for https://github.com/vonovak/react-navigation-header-buttons/blob/master/INSTALL.md
         unstable_enablePackageExports: true,
         // for zustand https://github.com/pmndrs/zustand/issues/2329#issuecomment-2053692578
         unstable_conditionNames: ['require'],
         sourceExts: [
            ...sourceExts,
            'mjs',
            // for firebase https://docs.expo.dev/guides/using-firebase/#configure-metro
            'cjs'
         ],
      },
   };
})();