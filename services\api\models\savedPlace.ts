import { Address } from "react-native-maps";
import { Geometry } from "../../../models/geometry";
import { PlaceType } from "./placeType";

export interface SavedPlaceBase {
    readonly name: string;
    readonly type: string;
    readonly geometry: Geometry;
    readonly note?: string;
}

export interface SavedPlaceCreationRequest extends SavedPlaceBase {

}

export interface AddSavedPlaceTagRequest {
    readonly savedPlaceType: PlaceType;
    readonly tag: 'HIDDEN'
}

export interface SavedPlaceUpdateRequest {
    readonly name: string;
    readonly note?: string;
}

export interface SavedPlace extends SavedPlaceBase {
    readonly id: number;
    readonly placeId: string;
    readonly address?: Address;
    readonly formattedAddress?: string;
    readonly tags: ReadonlyArray<'HIDDEN'>;
    readonly createdAt: Date;
    readonly updatedAt?: Date;
}
