import * as React from 'react';
import { StyleSheet } from "react-native";
import { FAB, Portal, useTheme } from 'react-native-paper';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';

interface Props {
    readonly visible: boolean;
    readonly onAddPharmacyPress: () => void;
    readonly onAddChainagePress: () => void;
}

const AddMarkerFAB = (props: Props) => {

    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);
    const theme = useTheme();

    const [mainFabOpen, setMainFabOpen] = React.useState<boolean>(false);

    return (
        <Portal>
            <FAB.Group
                open={mainFabOpen}
                visible={props.visible}
                variant='surface'
                fabStyle={[styles.fab]}
                icon={mainFabOpen ? 'close-thick' : 'map-marker-plus'}
                actions={[
                    {
                        icon: 'shape-circle-plus',
                        label: translationService.translate("PLACE_CARD_ADD_CHAINAGE"),
                        color: theme.colors.primary,
                        onPress: props.onAddChainagePress,
                        style: styles.fabItem,
                        labelStyle: styles.fabItem,
                        size: 'medium'
                    },
                    {
                        icon: 'plus-circle-outline',
                        label: translationService.translate("PLACE_CARD_ADD_PHARMACY"),
                        color: 'orange',
                        size: 'medium',
                        style: styles.fabItem,
                        labelStyle: styles.fabItem,
                        onPress: props.onAddPharmacyPress,
                    }
                ]}
                onStateChange={({ open }) => setMainFabOpen(open)}
                onPress={() => {
                    if (mainFabOpen) {
                        // do something if the speed dial is open
                    }
                }}
            />
        </Portal>

    );
};

const styles = StyleSheet.create({
    fab: {
        position: 'absolute',
        right: 0,
    },
    fabItem: {
        bottom: 132,
    }
});

export default AddMarkerFAB;