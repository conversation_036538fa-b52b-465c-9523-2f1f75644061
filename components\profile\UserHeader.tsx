import _ from "lodash";
import { useContext } from "react";
import { StyleSheet, View } from "react-native";
import { Avatar, Icon, MD3Theme, Surface, Text, useTheme } from "react-native-paper";
import { UserInfo } from "../../models/userDetails";
import { translationServiceContext } from "../../services/provider";
import { TranslationService } from "../../services/translationService";

interface UserHeaderProps {
    readonly user: UserInfo;
    readonly subscribed: boolean;
};

const getFL = (email: string, firstName?: string, lastName?: string): string => {
    return _.chain([firstName, lastName, email])
        .filter(_.identity)
        .first()
        .thru((str) => str?.charAt(0) || "")
        .value();
};

const getDisplayName = (firstName?: string, lastName?: string): string => {
    return _.chain([firstName, lastName])
        .filter((str) => !_.isNil(str))
        .join(" ")
        .defaultTo("--")
        .value();
};

const UserHeader: React.FC<UserHeaderProps> = ({ user, subscribed }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const theme: MD3Theme = useTheme();

    const SubscriptionBadge: React.ReactNode = (
        <Surface
            elevation={2}
            style={[
                styles.badge,
                {
                    backgroundColor: theme.dark ? theme.colors.primaryContainer : '#4CAF50',
                    borderColor: theme.colors.background,
                }
            ]}
        >
            <Icon source="star" size={16} color={theme.dark ? theme.colors.onPrimaryContainer : '#FFFFFF'} />
        </Surface>
    );

    return (
        <View style={styles.profileSubHeaderContainer}>
            <View style={styles.avatarContainer}>
                <Avatar.Text size={56} label={getFL(user.email, user.firstName, user.lastName)} />
                {subscribed && SubscriptionBadge}
            </View>
            <View style={styles.userInfoContainer}>
                <Text variant='titleLarge'>{getDisplayName(user.firstName, user.lastName)}</Text>
                <Text variant='bodyLarge'>{translationService.translate("SHORT_EMAIL")}: {user.email}</Text>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    badge: {
        position: 'absolute',
        bottom: -10,
        right: -10,  // Changed from -5 to -15 to move it more to the right
        paddingHorizontal: 8,
        paddingVertical: 2,
        borderRadius: 12,
        borderWidth: 1,
        zIndex: 1,
    },
    profileSubHeaderContainer: {
        alignItems: 'center',
        marginBottom: 16,
        gap: 8
    },
    avatarContainer: {
        position: 'relative',
        marginBottom: 8,
    },
    userInfoContainer: {
        gap: 8,
        alignItems: 'center'
    }
});

export default UserHeader;
