import { createNativeStackNavigator as createStackNavigator } from '@react-navigation/native-stack';
import React, { useContext } from 'react';
import HomeScreen from '../../../../screens/user/HomeScreen';
import SearchScreen from '../../../../screens/user/SearchScreen';
import { translationServiceContext } from '../../../../services/provider';
import { TranslationService } from '../../../../services/translationService';

const HomeStackNavigator = createStackNavigator<HomeStackParamList>();

function HomeStack() {
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const appTitle: string = translationService.translate("APP_TITLE");

    return (
        <HomeStackNavigator.Navigator>
            <HomeStackNavigator.Screen
                name="Home"
                component={HomeScreen}
                options={{
                    title: appTitle,
                    headerShown: true
                }}
            />
            <HomeStackNavigator.Screen
                name="Search"
                component={SearchScreen}
                options={{
                    title: appTitle,
                    headerShown: true,
                    headerBackTitleVisible: false,
                    animation: 'none'
                }}
            />
        </HomeStackNavigator.Navigator>
    )
};

export default HomeStack;