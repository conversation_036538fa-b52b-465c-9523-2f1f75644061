import * as Location from 'expo-location';
import { Alert, Linking, Platform } from 'react-native';
import { TranslationService } from '../services/translationService'; // Adjust the path as needed

export const getForegroundPermissionStatus = async (): Promise<Location.PermissionStatus> => {
    const { status } = await Location.getForegroundPermissionsAsync();
    return status;
};

export const requestForegroundPermission = async (): Promise<Location.PermissionStatus> => {
    const { status } = await Location.requestForegroundPermissionsAsync();
    return status;
};

export const openAppSettings = () => {
    if (Platform.OS === 'ios') {
        Linking.openURL('app-settings:');
    } else {
        Linking.openSettings();
    }
};

export const showPermissionDeniedAlert = (translationService: TranslationService) => {
    Alert.alert(
        translationService.translate('PERMISSION_DENIED_TITLE'),
        translationService.translate('PERMISSION_DENIED_MESSAGE'),
        [
            {
                text: translationService.translate('CANCEL'),
                style: 'cancel',
            },
            {
                text: translationService.translate('OPEN_SETTINGS'),
                onPress: openAppSettings,
            },
        ],
        { cancelable: false }
    );
};
