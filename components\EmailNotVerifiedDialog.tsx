import * as React from 'react';
import { StyleSheet } from "react-native";
import { Button, Dialog, Portal, Text } from 'react-native-paper';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';

interface Props {
    readonly visible: boolean;
    readonly onResendVerificationEmail: () => void;
    readonly onDismiss: () => void;
}

const EmailNotVerifiedDialog = (props: Props) => {

    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);

    const [confirmationVisible, setConfirmationVisible] = React.useState<boolean>(false);

    const handleConfirmDismiss = () => {
        setConfirmationVisible(false);
    };

    const handleResend = () => {
        props.onResendVerificationEmail();
        setConfirmationVisible(true);
    };

    return (
        <Portal>
            <Dialog visible={props.visible} onDismiss={props.onDismiss}>
                <Dialog.Icon icon="alert-circle-outline" size={40} />
                <Dialog.Title>{translationService.translate("EMAIL_NOT_VERIFIED_DIALOG_TITLE")}</Dialog.Title>
                <Dialog.Content>
                    <Text variant="bodyLarge">{translationService.translate("EMAIL_NOT_VERIFIED_DIALOG_CONTENT")}</Text>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={props.onDismiss}>
                        {translationService.translate("CANCEL")}
                    </Button>
                    <Button icon="email-sync-outline" onPress={handleResend}>
                        {translationService.translate("RESEND_VERIFICATION_EMAIL")}
                    </Button>
                </Dialog.Actions>
            </Dialog>

            <Dialog visible={confirmationVisible} onDismiss={handleConfirmDismiss}>
                <Dialog.Icon icon="check-circle-outline" size={40} />
                <Dialog.Title>{translationService.translate("VERIFICATION_EMAIL_SENT_DIALOG_TITLE")}</Dialog.Title>
                <Dialog.Content>
                    <Text variant="bodyLarge">{translationService.translate("VERIFICATION_EMAIL_SENT_DIALOG_CONTENT")}</Text>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={handleConfirmDismiss}>
                        {translationService.translate("OK")}
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
});


export default EmailNotVerifiedDialog;