import React from 'react';
import { StyleSheet } from 'react-native';
import { Button, Dialog, Portal, Text } from 'react-native-paper';
import { TranslationService } from '../services/translationService';

interface Props {
    readonly visible: boolean;
    readonly onDismiss: () => void;
    readonly onConfirm: () => void;
    readonly isLoading: boolean;
    readonly translationService: TranslationService;
}

const SignOutConfirmationDialog: React.FC<Props> = ({ 
    visible, 
    onDismiss, 
    onConfirm, 
    isLoading,
    translationService 
}) => {
    return (
        <Portal>
            <Dialog visible={visible} onDismiss={onDismiss}>
                <Dialog.Icon icon="logout" size={40} />
                <Dialog.Title>{translationService.translate("SIGN_OUT_CONFIRMATION_TITLE")}</Dialog.Title>
                <Dialog.Content>
                    <Text variant="bodyLarge">
                        {translationService.translate("SIGN_OUT_CONFIRMATION_CONTENT")}
                    </Text>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={onDismiss} disabled={isLoading}>
                        {translationService.translate("CANCEL")}
                    </Button>
                    <Button onPress={onConfirm} loading={isLoading}>
                        {translationService.translate("SIGN_OUT")}
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({});

export default SignOutConfirmationDialog;
