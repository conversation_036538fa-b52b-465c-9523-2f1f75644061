import * as React from 'react';
import { StyleSheet } from "react-native";
import { FAB } from 'react-native-paper';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';

interface Props {
    readonly visible: boolean;
    readonly onPress: () => void;
    readonly loading: boolean;
}

const CurrentLocationFAB = (props: Props) => {

    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);

    return (
        <FAB
            visible={props.visible}
            loading={props.loading}
            icon={`crosshairs-gps`}
            mode="elevated"
            variant='surface'
            style={[styles.fab]}
            onPress={props.onPress}
        />
    );
};

const styles = StyleSheet.create({
    fab: {
        position: 'absolute',
        bottom: 92, // Position at the bottom-right corner
    },
});

export default CurrentLocationFAB;