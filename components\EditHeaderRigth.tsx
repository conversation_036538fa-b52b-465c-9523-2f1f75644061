import React, { useContext } from 'react';
// @ts-ignore
import { HeaderButtons, Item } from 'react-navigation-header-buttons';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';
import { MaterialHeaderButton } from './MaterialHeaderButton';

interface Props {
    readonly onSave?: () => void;
    readonly saving?: boolean;
};

const EditHeaderRight: React.FC<Props> = ({ onSave, saving }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const saveLabel: string = translationService.translate("SAVE");

    return (
        <HeaderButtons HeaderButtonComponent={MaterialHeaderButton}>
            <Item
                iconName="check"
                title={saveLabel}
                disabled={saving}
                onPress={onSave}
            />
        </HeaderButtons>
    )
};

export default EditHeaderRight;
