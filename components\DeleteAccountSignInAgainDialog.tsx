import * as React from 'react';
import { StyleSheet } from "react-native";
import { Button, Dialog, Portal, Text } from 'react-native-paper';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';

interface Props {
    readonly visible: boolean;
    readonly onSignInAgain: () => void;
    readonly onDismiss: () => void;
}

const DeleteAccountSignInAgainDialog = (props: Props) => {

    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);

    return (
        <Portal>
            <Dialog visible={props.visible} onDismiss={props.onDismiss}>
                <Dialog.Icon icon="login" size={40} />
                <Dialog.Title>{translationService.translate("DELETE_ACCOUNT_SIGN_IN_AGAIN_DIALOG_TITLE")}</Dialog.Title>
                <Dialog.Content>
                    <Text variant="bodyLarge">{translationService.translate("DELETE_ACCOUNT_SIGN_IN_AGAIN_DIALOG_CONTENT")}</Text>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={props.onDismiss}>
                        {translationService.translate("CANCEL")}
                    </Button>
                    <Button onPress={props.onSignInAgain}>
                        {translationService.translate("SIGN_IN_AGAIN")}
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
});


export default DeleteAccountSignInAgainDialog;