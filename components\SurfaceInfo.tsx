import React from 'react';
import { StyleSheet, View } from "react-native";
import { Icon, Surface, Text, useTheme } from "react-native-paper";

interface Props {
    readonly icon: string;
    readonly iconColor?: string;
    readonly text: string;
}

const SurfaceInfo: React.FC<Props> = ({ icon, iconColor, text }) => {

    const theme = useTheme();

    return (
        <Surface style={[styles.surface, {borderRadius: theme.roundness}]} mode="flat">
            <View style={styles.surfaceView}>
                <View style={styles.iconView}>
                    <Icon source={icon} size={30} color={iconColor ?? "green"} />
                </View>
                <Text variant="bodyMedium">
                    {text}
                </Text>
            </View>
        </Surface>
    );
};

const styles = StyleSheet.create({
    surface: {
        padding: 16
    },
    surfaceView: {
        flexDirection: "row",
        alignItems: "center"
    },
    iconView: {
        marginRight: 8
    }
});

export default SurfaceInfo;
