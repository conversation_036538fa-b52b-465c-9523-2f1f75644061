import { NativeStackScreenProps } from "@react-navigation/native-stack";
import { useFormik } from 'formik';
import _ from 'lodash';
import React, { useContext, useEffect } from "react";
import { StyleSheet, View } from 'react-native';
import { HelperText, ProgressBar, TextInput } from "react-native-paper";
import * as Yup from 'yup';
import EditHeaderRight from "../../../components/EditHeaderRigth";
import SurfaceInfo from "../../../components/SurfaceInfo";
import { PlaceType } from "../../../models/constants";
import { Geometry } from "../../../models/geometry";
import { ApiService } from "../../../services/api/apiService";
import { SavedPlace } from "../../../services/api/models/savedPlace";
import { authSelectors } from "../../../services/authService";
import useGlobalStore from '../../../services/globalState';
import { apiServiceContext, translationServiceContext } from "../../../services/provider";
import { TranslationService } from "../../../services/translationService";

type SavedPlaceAddModalScreenProps = NativeStackScreenProps<MainUserStackParamList, 'SavedPlaceAdd'>;

const SavedPlaceAddModal: React.FC<SavedPlaceAddModalScreenProps> = ({ navigation, route }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const apiService: ApiService = useContext<ApiService>(apiServiceContext);
    
    const { geometry, placeType } = route.params;
    
    const userUid: string = useGlobalStore(authSelectors.authenticatedUserUid);
    
    const addSavedPlace = useGlobalStore((store) => store.addSavedPlace);
    const showSnackbar = useGlobalStore((store) => store.showSnackbar);

    const formik = useFormik({
        initialValues: {
            name: _.cond([
                [(pt: PlaceType) => pt === PlaceType.PHARMACY, () => translationService.translate("PHARMACY")],
                [(pt: PlaceType) => pt === PlaceType.CHAINAGE, () => translationService.translate("CHAINAGE")],
                [_.stubTrue, () => '']
            ])(placeType),
            note: ''
        },
        validationSchema: Yup.object({
            name: Yup.string().required(translationService.translate("REQUIRED_FIELD")),
            note: Yup.string(),
        }),
        onSubmit: (values, formikActions) => {
            addUserSavedPlace(values.name, placeType, geometry, values.note)
                .then((createdSavedPlace) => {
                    // @ts-ignore
                    navigation.navigate({
                        name: 'Home',
                        params: {
                            // todo: passing placeId is enough to know the geometry
                            geometry: {
                                lat: createdSavedPlace.geometry.lat,
                                lng: createdSavedPlace.geometry.lng
                            },
                            placeId: createdSavedPlace.placeId
                        }
                    });
                })
                .finally(() => {
                    formikActions.setSubmitting(false);
                })
        }
    });

    useEffect(() => {
        navigation.setOptions({
            headerTitle: `${translationService.translate("ADD")} ${translationService.translate(placeType)}`,
            headerBackButtonMenuEnabled: true,
            headerBackVisible: true,
            headerRight: () => (<EditHeaderRight onSave={() => formik.handleSubmit()} saving={formik.isSubmitting} />),
        });
    }, [navigation, userUid, formik]);

    const addUserSavedPlace = async (name: string, placeType: PlaceType, geometry: Geometry, note?: string): Promise<SavedPlace> => {
        try {
            const createdSavedPlace = await apiService.addUserSavedPlace(userUid, { name, type: placeType, geometry, note });
            addSavedPlace(createdSavedPlace);
            showSnackbar(
                _.cond([
                    [(pt: PlaceType) => pt === PlaceType.PHARMACY, () => translationService.translate('PHARMACY_ADDED_SUCCESS')],
                    [(pt: PlaceType) => pt === PlaceType.CHAINAGE, () => translationService.translate('CHAINAGE_ADDED_SUCCESS')],
                    [_.stubTrue, () => translationService.translate('ADD_SUCCESS')]
                ])(placeType),
                'success'
            );
            return createdSavedPlace;
        } catch (error) {
            showSnackbar(
                _.cond([
                    [(pt: PlaceType) => pt === PlaceType.PHARMACY, () => translationService.translate('PHARMACY_ADD_FAILED')],
                    [(pt: PlaceType) => pt === PlaceType.CHAINAGE, () => translationService.translate('CHAINAGE_ADD_FAILED')],
                    [_.stubTrue, () => translationService.translate('ADD_FAILED')]
                ])(placeType),
                'error'
            );
            throw error;
        }
    };

    return (
        <View>
            <ProgressBar visible={formik.isSubmitting} indeterminate />
            <View style={styles.container}>
                <SurfaceInfo icon="lock-check" text={translationService.translate("SECURITY_CONCERN_MSG_SAVED_PLACES")} />
                <View style={styles.formContainer}>
                    <View>
                        <TextInput
                            onChangeText={formik.handleChange('name')}
                            onBlur={formik.handleBlur('name')}
                            value={formik.values.name}
                            autoFocus
                            disabled={formik.isSubmitting}
                            mode="outlined"
                            label={translationService.translate("NAME_LABEL")}
                        />
                        {formik.touched.name && formik.errors.name ? (
                            <HelperText type="error">{formik.errors.name}</HelperText>
                        ) : null}
                    </View>
                    <View>
                        <TextInput
                            onChangeText={formik.handleChange('note')}
                            onBlur={formik.handleBlur('note')}
                            multiline
                            disabled={formik.isSubmitting}
                            value={formik.values.note}
                            mode="outlined"
                            label={translationService.translate("NOTE_LABEL")}
                        />
                        {formik.touched.note && formik.errors.note ? (
                            <HelperText type="error">{formik.errors.note}</HelperText>
                        ) : null}
                    </View>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        padding: 16,
        gap: 16
    },
    formContainer: {
        flexDirection: 'column',
        gap: 8,
    },
    surface: {
        padding: 16
    },
    surfaceView: {
        flexDirection: "row",
        alignItems: "center"
    },
    iconView: {
        marginRight: 8
    }
});

export default SavedPlaceAddModal;
