import React from "react";
import { StyleSheet } from "react-native";
import { ActivityIndicator, Button, Dialog, Portal, Text } from "react-native-paper";
import useGlobalStore from "../../services/globalState";
import { translationServiceContext } from "../../services/provider";
import { TranslationService } from "../../services/translationService";

const PurchaseFlowDialogs: React.FC = () => {

    const translationService: TranslationService = React.useContext(translationServiceContext);

    const {
        checkingPurchase,
        subscriptionSuccessVisible,
        setSubscriptionSucessVisible,
    } = useGlobalStore();

    return (
        <Portal>
            <Dialog visible={checkingPurchase} dismissable={false}>
                <Dialog.Content style={styles.loadingDialogContent}>
                    <ActivityIndicator animating={true} size="large" />
                    <Text variant="bodyLarge" style={styles.loadingText}>
                        {translationService.translate("CHECKING_PURCHASE")}
                    </Text>
                </Dialog.Content>
            </Dialog>

            <Dialog visible={subscriptionSuccessVisible} onDismiss={() => setSubscriptionSucessVisible(false)}>
                <Dialog.Icon icon="check-circle" size={40} />
                <Dialog.Title>{translationService.translate("PURCHASE_SUCCESS_TITLE")}</Dialog.Title>
                <Dialog.Content>
                    <Text variant="bodyLarge">
                        {translationService.translate("PURCHASE_SUCCESS_CONTENT")}
                    </Text>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={() => setSubscriptionSucessVisible(false)} uppercase>
                        {translationService.translate("ENJOY")}
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    loadingDialogContent: {
        alignItems: 'center',
        gap: 16,
    },
    loadingText: {
        textAlign: 'center',
    }
});

export default PurchaseFlowDialogs;
