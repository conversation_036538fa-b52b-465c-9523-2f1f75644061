import * as React from 'react';
import { StyleSheet, View } from "react-native";
import { ActivityIndicator, Portal, Text, useTheme } from 'react-native-paper';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';

const AuthLoadingIndicator = () => {
    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);
    const theme = useTheme();

    return (
        <Portal>
            <View style={[styles.overlay, { backgroundColor: theme.colors.backdrop }]}>
                <View style={[styles.loadingContainer, { backgroundColor: theme.colors.surface }]}>
                    <ActivityIndicator animating={true} size="large" />
                    <Text variant="bodyLarge" style={styles.loadingText}>
                        {translationService.translate("AUTHENTICATING")}
                    </Text>
                </View>
            </View>
        </Portal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        ...StyleSheet.absoluteFillObject,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 9000,
    },
    loadingContainer: {
        padding: 24,
        borderRadius: 8,
        alignItems: 'center',
        justifyContent: 'center',
        elevation: 4,
        gap: 16,
    },
    loadingText: {
        textAlign: 'center',
    },
});

export default AuthLoadingIndicator;



