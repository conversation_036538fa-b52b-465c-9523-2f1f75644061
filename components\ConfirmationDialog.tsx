import { Alert } from "react-native";

interface Props {
    readonly title: string;
    readonly description?: string;
    readonly primaryActionLabel: string;
    readonly cancelActionLabel: string;
    readonly onPrimaryAction: () => void;
    readonly onCancel: () => void;
}

export default function confirmationDialog(props: Props) {
    Alert.alert(
        props.title,
        props.description,
        [
            {
                text: props.cancelActionLabel,
                onPress: props.onCancel,
                style: 'cancel',
            },
            {
                text: props.primaryActionLabel,
                onPress: props.onPrimaryAction,
            }
        ]
    );
};