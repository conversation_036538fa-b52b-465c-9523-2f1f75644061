import React, { useContext, useState } from 'react';
import { StyleSheet, View } from "react-native";
import { Button, Dialog, HelperText, Portal, TextInput } from "react-native-paper";
import { FIELD_NAME } from '../models/field';
import { CustomPlace } from '../models/place';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';

interface Props {
    readonly title: string;
    readonly savedPlace: CustomPlace;
    readonly fieldName: FIELD_NAME;
    readonly required?: boolean;
    readonly visible: boolean;
    readonly onDismiss: () => void;
    readonly onSubmit: (savedPlace: CustomPlace) => Promise<void>;
};

const getFieldValue = (savedPlace: CustomPlace, fieldName: FIELD_NAME) => {
    if (FIELD_NAME.NAME === fieldName) {
        return savedPlace.name;
    } else if (FIELD_NAME.NOTE === fieldName) {
        return savedPlace.note;
    } else {
        throw Error("Unknown field name.");
    }
};

const UpdateFieldDialog: React.FC<Props> = ({ savedPlace, fieldName, visible, title, required = false, onDismiss, onSubmit }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const savingLabel: string = translationService.translate("SAVING");
    const saveLabel: string = translationService.translate("SAVE");
    const cancelLabel: string = translationService.translate("CANCEL");
    const requiredFieldMessage: string = translationService.translate("REQUIRED_FIELD");
    const nameFieldLabel: string = translationService.translate("FIELD_NAME");
    const noteFieldLabel: string = translationService.translate("FIELD_NOTE");

    const [selectAllText, setSelectAllText] = useState<boolean>(true);
    const [fieldValue, setFieldValue] = useState<string | undefined>(getFieldValue(savedPlace, fieldName));
    const [saving, setSaving] = useState<boolean>(false);

    const getUpdatedSavedPlace = () => {
        let updatedSavedPlace: CustomPlace = savedPlace;
        if (FIELD_NAME.NAME === fieldName && fieldValue) {
            updatedSavedPlace = { ...updatedSavedPlace, name: fieldValue };
        } else if (FIELD_NAME.NOTE === fieldName) {
            updatedSavedPlace = { ...updatedSavedPlace, note: fieldValue };
        } else {
            throw Error("Unknow field name.");
        }
        return updatedSavedPlace;
    };

    const isValid: () => boolean = () => {
        if (!required) return true;
        if (fieldValue) {
            return fieldValue.length > 0;
        } else {
            return false;
        }
    };

    const onCancel = () => {
        onDismiss();
        clearForm();
    };

    const clearForm = () => {
        setFieldValue("");
    };

    const onSave = async () => {
        if (isValid()) {
            setSaving(true);
            onSubmit(getUpdatedSavedPlace())
                .finally(() => {
                    clearForm();
                    setSaving(false);
                });
        }
    };

    return (
        <Portal>
            <Dialog visible={visible} onDismiss={onCancel}>
                <Dialog.Title style={styles.dialogTitle}>{title}</Dialog.Title>
                <Dialog.Content>
                    <View style={styles.formContainer}>
                        <View>
                            <TextInput
                                label={fieldName === FIELD_NAME.NAME ? nameFieldLabel : noteFieldLabel}
                                defaultValue={fieldValue}
                                disabled={saving}
                                autoFocus
                                multiline={FIELD_NAME.NOTE === fieldName}
                                selection={selectAllText && fieldValue ? { start: 0, end: fieldValue.length } : undefined}
                                onBlur={() => setSelectAllText(false)}
                                onKeyPress={() => setSelectAllText(false)}
                                onTouchStart={() => setSelectAllText(false)}
                                onChangeText={nextValue => {
                                    setFieldValue(nextValue);
                                    setSelectAllText(false);
                                }}
                            />
                            <HelperText type="error" visible={!isValid()}>
                                {requiredFieldMessage}
                            </HelperText>
                        </View>
                    </View>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button onPress={onCancel} disabled={saving}>
                        {cancelLabel}
                    </Button>
                    <Button mode="contained-tonal" onPress={onSave} disabled={!isValid() || saving}>
                        {saving ? `${savingLabel}...` : saveLabel}
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    dialogTitle: {
        textAlign: 'center'
    },
    formContainer: {
        flexDirection: 'column',
        gap: 8
    }
});

export default UpdateFieldDialog;
