import { FIELD_NAME } from "../models/field";
import { CustomPlace } from "../models/place";

export function isFieldValid(required: boolean, fieldValue: any): boolean {
    if (!required) {
        return true;
    } else {
        if (fieldValue) {
            return fieldValue.length > 0;
        } else {
            return false;
        }
    }
};

export function getSavedPlaceFieldNameValue(savedPlace: CustomPlace, fieldName: FIELD_NAME): any {
    switch (fieldName) {
        case FIELD_NAME.NAME: return savedPlace.name;
        case FIELD_NAME.NOTE: return savedPlace.note;
        default: throw Error("Unknown field name");
    }
};

export function getMappedSavedPlace(savedPlace: CustomPlace, fieldName: FIELD_NAME, fieldValue: any): CustomPlace {
    if (FIELD_NAME.NAME === fieldName) {
        return { ...savedPlace, name: fieldValue };
    } else if (FIELD_NAME.NOTE === fieldName) {
        return { ...savedPlace, note: fieldValue };
    } else {
        throw Error("Unknow field name.");
    }
};