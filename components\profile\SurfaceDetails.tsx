import React, { ReactNode } from 'react';
import { StyleSheet } from 'react-native';
import { Surface, Text, useTheme } from 'react-native-paper';

interface Props {
    readonly title: string;
    readonly titleColor?: string;
    readonly subtitle?: string;
    readonly action?: ReactNode;
}

const SurfaceDetails: React.FC<Props> = ({ title, titleColor, subtitle, action }) => {

    const theme = useTheme();

    return (
        <Surface style={[styles.container, { borderRadius: theme.roundness * 2 }]} mode="flat">
            <Text
                variant='titleMedium'
                style={{
                    ...styles.textAlignCenter,
                    ...(titleColor !== undefined ? { color: titleColor } : {}) 
                }}
            >
                {title}
            </Text>
            <Text variant='bodyMedium' style={styles.textAlignCenter}>
                {subtitle}
            </Text>
            {action}
        </Surface>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 16,
        borderColor: 'gray',
        borderWidth: 1,
        gap: 6,
        marginVertical: 8,
    },
    textAlignCenter: {
        textAlign: 'center',
    }
});

export default SurfaceDetails;
