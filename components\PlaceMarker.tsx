import React from 'react';
import { Circle, Marker } from "react-native-maps";
import { PlaceType } from '../models/constants';
import { Place } from "../models/place";
import { generateLatLng } from "../utils/others";
import { Image } from 'react-native';

interface Props {
    readonly place: Place;
    readonly selected: boolean;
    readonly showCircle?: boolean;
    readonly circleDiameter?: number;
    readonly markerColor?: string;
    readonly fillColor?: string;
    readonly strokeColor?: string;
    readonly onPress?: () => void;
}

const generateCircleKey = (lat: number, lng: number, radius: number = 0, selected: boolean = false): string => {
    return `cir-${generateLatLng(lat, lng)}-${radius}-${selected}`;
};
const PlaceMarker: React.FC<Props> = ({
    place,
    selected,
    circleDiameter,
    fillColor,
    strokeColor,
    markerColor,
    showCircle = true,
    onPress
}) => {
    return (
        <React.Fragment>
            <Marker
                key={`${place.placeId}-${place.geometry.lat},${place.geometry.lng}-${selected}`} // Changing the key forces a re-render
                pinColor={markerColor}
                coordinate={{ latitude: place.geometry.lat, longitude: place.geometry.lng }}
                // to disable the callout view
                // title={place.name}
                onPress={onPress}
            >
                {selected && place.type === PlaceType.EXISTING_PHARMACY && (
                    <Image
                        source={require('../assets/images/pins/red-white-border-pin.png')}
                        style={{ width: 26, height: 37 }}
                    />
                )}
                {selected && place.type === PlaceType.PHARMACY && (
                    <Image
                        source={require('../assets/images/pins/orange-white-border-pin.png')}
                        style={{ width: 26, height: 37 }}
                    />
                )}
                {selected && place.type === PlaceType.CHAINAGE && (
                    <Image
                        source={require('../assets/images/pins/green-white-border-pin.png')}
                        style={{ width: 26, height: 37 }}
                    />
                )}
            </Marker>
            {showCircle && circleDiameter && !(PlaceType.SEARCH_RESULT === place.type) && (
                <Circle
                    key={generateCircleKey(place.geometry.lat, place.geometry.lng, circleDiameter, selected)}
                    center={{ latitude: place.geometry.lat, longitude: place.geometry.lng }}
                    fillColor={fillColor}
                    strokeColor={selected ? "#ffffff" : strokeColor}
                    strokeWidth={2}
                    radius={circleDiameter}
                    style={{
                        zIndex: selected ? 999 : undefined
                    }}
                />
            )}
        </React.Fragment>
    );
};

export default PlaceMarker;
