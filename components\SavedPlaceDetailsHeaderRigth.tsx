import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import React from 'react';
import { Divider, HeaderButtons, HiddenItem, Item, OverflowMenu } from 'react-navigation-header-buttons';
import { MaterialHeaderButton } from './MaterialHeaderButton';

interface Props {
  readonly onMapPress?: () => void;
  readonly onDeletePress?: () => void;
  readonly onGoogleMapsPress?: () => void;
};

const SavedPlaceDetailsHeaderRight: React.FC<Props> = ({ onMapPress, onDeletePress, onGoogleMapsPress }) => {
  return (
    <HeaderButtons HeaderButtonComponent={MaterialHeaderButton}>
      <Item
        title="search"
        iconName="remove-red-eye"
        onPress={onMapPress}
      />
      <OverflowMenu
        OverflowIcon={({ color }) => (
          <MaterialIcons name="more-horiz" size={23} color={color} />
        )}
      >
        <HiddenItem title="Supprimer" onPress={onDeletePress} />
        <Divider />
        <HiddenItem title="Voir dans Google Maps" onPress={onGoogleMapsPress} />
      </OverflowMenu>
    </HeaderButtons>
  )
};

export default SavedPlaceDetailsHeaderRight;
