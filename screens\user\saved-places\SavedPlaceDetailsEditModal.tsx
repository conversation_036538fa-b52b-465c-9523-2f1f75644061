import { NativeStackScreenProps } from "@react-navigation/native-stack";
import _ from "lodash";
import React, { useContext, useEffect, useState } from "react";
import { StyleSheet, View } from 'react-native';
import { HelperText, ProgressBar, TextInput } from "react-native-paper";
import EditHeaderRight from "../../../components/EditHeaderRigth";
import { PlaceType } from "../../../models/constants";
import { CustomPlace } from '../../../models/place';
import { ApiService } from "../../../services/api/apiService";
import { authSelectors } from "../../../services/authService";
import useGlobalStore from '../../../services/globalState';
import { apiServiceContext, formsServiceContext, translationServiceContext } from "../../../services/provider";
import { TranslationService } from "../../../services/translationService";
import { FieldDetails, YupFormsService } from "../../../services/yupFormsService";
import { getMappedSavedPlace, getSavedPlaceFieldNameValue, isFieldValid } from "../../../utils/fields";
import { requireNonNull } from "../../../utils/others";

type SavedPlaceDetailsEditModalScreenProps = NativeStackScreenProps<MainUserStackParamList, 'SavedPlaceDetailsEdit'>;

const SavedPlaceDetailsEditModal: React.FC<SavedPlaceDetailsEditModalScreenProps> = ({ navigation, route }) => {

    const userUid: string = useGlobalStore(authSelectors.authenticatedUserUid);

    const { placeId, fieldName } = route.params;

    const formsService: YupFormsService = useContext<YupFormsService>(formsServiceContext);
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const apiService: ApiService = useContext<ApiService>(apiServiceContext);

    const fieldDetails: FieldDetails = requireNonNull(formsService.getSavedPlaceForm().get(fieldName));

    const updateSavedPlace = useGlobalStore((store) => store.updateSavedPlace);
    const findRequiredSavedPlace: (placeId: string) => CustomPlace = useGlobalStore((store) => store.findRequiredSavedPlace);

    const showSnackbar = useGlobalStore((state) => state.showSnackbar);

    const savedPlace: CustomPlace = findRequiredSavedPlace(placeId);

    const [selectAllText, setSelectAllText] = useState<boolean>(true);
    const [fieldValue, setFieldValue] = useState<string | undefined>(getSavedPlaceFieldNameValue(savedPlace, fieldName));
    const [saving, setSaving] = useState<boolean>(false);

    useEffect(() => {
        navigation.setOptions({
            headerTitle: `${translationService.translate("UPDATE")} ${fieldDetails.label}`,
            headerBackButtonMenuEnabled: true,
            headerBackVisible: true,
            headerBackTitle: translationService.translate("BACK"),
            headerRight: () => (<EditHeaderRight onSave={onSave} saving={saving} />),
        });
    }, [navigation, userUid, saving, savedPlace, fieldName, fieldValue]);

    const updateUserSavedPlace = async (savedPlace: CustomPlace) => {
        try {
            const updatedSavedPlace = await apiService.updateUserSavedPlace(
                userUid,
                savedPlace.placeId,
                {
                    name: savedPlace.name,
                    note: savedPlace.note,
                }
            );
            updateSavedPlace(updatedSavedPlace);
            showSnackbar(
                _.cond([
                    [(pt: PlaceType) => pt === PlaceType.PHARMACY, () => translationService.translate('PHARMACY_UPDATE_SUCCESS')],
                    [(pt: PlaceType) => pt === PlaceType.CHAINAGE, () => translationService.translate('CHAINAGE_UPDATE_SUCCESS')],
                    [_.stubTrue, () => translationService.translate('PLACE_UPDATE_SUCCESS')]
                ])(savedPlace.type),
                'success'
            );
        } catch (error) {
            const errorMessage: string = error instanceof Error ? error.message : String(error);
            showSnackbar(
                _.cond([
                    [(pt: PlaceType) => pt === PlaceType.PHARMACY, () => translationService.translate('PHARMACY_UPDATE_FAIL')],
                    [(pt: PlaceType) => pt === PlaceType.CHAINAGE, () => translationService.translate('CHAINAGE_UPDATE_FAIL')],
                    [_.stubTrue, () => translationService.translate('PLACE_UPDATE_FAIL')]
                ])(savedPlace.type),
                'error'
            );
            throw new Error(`Failed to update saved place: ${errorMessage}`);
        }
    };

    const isValid = () => {
        return isFieldValid(fieldDetails.required, fieldValue);
    };

    const onSave = async () => {
        if (isValid()) {
            setSaving(true);
            const mappedSavedPlace: CustomPlace = getMappedSavedPlace(savedPlace, fieldName, fieldValue);
            updateUserSavedPlace(mappedSavedPlace)
                .finally(() => {
                    setSaving(false);
                    navigation.goBack();
                });
        }
    };

    return (
        <View>
            <ProgressBar visible={saving} indeterminate />
            <View style={styles.formContainer}>
                <TextInput
                    label={fieldDetails.label}
                    value={fieldValue}
                    disabled={saving}
                    autoFocus
                    multiline={fieldDetails.multiline}
                    mode="outlined"
                    error={!isValid()}
                    selection={selectAllText && fieldValue ? { start: 0, end: fieldValue.length } : undefined}
                    onBlur={() => setSelectAllText(false)}
                    onKeyPress={() => setSelectAllText(false)}
                    onTouchStart={() => setSelectAllText(false)}
                    onChangeText={nextValue => {
                        setFieldValue(nextValue);
                        setSelectAllText(false);
                    }}
                />
                <HelperText type="error" visible={!isValid()}>
                    {translationService.translate("REQUIRED_FIELD")}
                </HelperText>
            </View>
        </View>

    );
};

const styles = StyleSheet.create({
    formContainer: {
        flexDirection: 'column',
        gap: 8,
        padding: 16,
    }
});

export default SavedPlaceDetailsEditModal;
