import * as React from 'react';
import { StyleSheet } from 'react-native';
import { Button, ButtonProps } from 'react-native-paper';
import { translationServiceContext } from '../../services/provider';
import { TranslationService } from '../../services/translationService';

interface Props extends Omit<ButtonProps, 'children'> {
    readonly buttonColor?: string;
    readonly uppercase?: boolean;
}

const SubscribeButton: React.FC<Props> = ({
    mode = "contained",
    buttonColor = "orange",
    uppercase = true,
    style,
    ...buttonProps
}) => {

    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);

    return (
        <Button
            mode={mode}
            buttonColor={buttonColor}
            style={[styles.selfSizing, style]}
            uppercase={uppercase}
            contentStyle={{ alignSelf: 'center' }}
            {...buttonProps}
        >
            {translationService.translate("SUBSCRIBE")}
        </Button>
    );
};

const styles = StyleSheet.create({
    selfSizing: {
        alignSelf: 'center',
        width: 'auto'
    }
});

export default SubscribeButton;