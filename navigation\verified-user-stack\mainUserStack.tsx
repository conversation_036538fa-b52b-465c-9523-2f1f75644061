import { createNativeStackNavigator as createStackNavigator } from '@react-navigation/native-stack';
import React, { useContext } from 'react';
import EditHeaderRight from '../../components/EditHeaderRigth';
import SavedPlaceDetailsHeaderRight from '../../components/SavedPlaceDetailsHeaderRigth';
import AuthSignUpFormScreen from '../../screens/auth/AuthSignUpFormScreen';
import UserPersonalInfoEditModal from '../../screens/user/account/profile/UserPersonalInfoEditModal';
import UserPersonalInfoScreen from '../../screens/user/account/profile/UserPersonalInfoScreen';
import SubscriptionScreen from '../../screens/user/account/subscription/SubscriptionScreen';
import SavedPlaceAddModal from '../../screens/user/saved-places/SavedPlaceAddModal';
import SavedPlaceDetailsEditModal from '../../screens/user/saved-places/SavedPlaceDetailsEditModal';
import SavedPlaceDetailsScreen from '../../screens/user/saved-places/SavedPlaceDetailsScreen';
import { translationServiceContext } from '../../services/provider';
import { TranslationService } from '../../services/translationService';
import UserTabsStack from './tabs/userTabsStack';

const MainUserStackNavigator = createStackNavigator<MainUserStackParamList>();

export default function MainUserStack() {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    return (
        <MainUserStackNavigator.Navigator>
            <MainUserStackNavigator.Group>
                <MainUserStackNavigator.Screen
                    name="Tabs"
                    component={UserTabsStack}
                    options={{
                        headerShown: false
                    }}
                />
                <MainUserStackNavigator.Screen
                    name="SavedPlaceDetails"
                    component={SavedPlaceDetailsScreen}
                    options={({ route }) => ({
                        title: route.params?.placeId,
                        headerBackTitleVisible: false,
                        headerRight: () => (<SavedPlaceDetailsHeaderRight />)
                    })}
                />
                <MainUserStackNavigator.Screen
                    name="UserPersonalInfo"
                    component={UserPersonalInfoScreen}
                    options={({ route }) => ({
                        title: translationService.translate("PERSONAL_INFO"),
                        headerBackTitleVisible: true,
                        headerBackTitle: translationService.translate("TABS_ACCOUNT")
                    })}
                />
                <MainUserStackNavigator.Screen
                    name="Subscription"
                    component={SubscriptionScreen}
                    options={({ navigation }) => ({
                        title: translationService.translate("SUBSCRIPTION_SCREEN_TITLE"),
                        headerBackTitleVisible: true,
                        headerBackTitle: translationService.translate("TABS_ACCOUNT")
                    })}
                />
                <MainUserStackNavigator.Screen
                    name="AuthSignUpForm"
                    component={AuthSignUpFormScreen}
                    options={{
                        title: translationService.translate("SIGN_UP_SCREEN_TITLE"),
                        headerBackTitleVisible: true,
                        headerBackButtonMenuEnabled: true
                    }}
                />
            </MainUserStackNavigator.Group>
            <MainUserStackNavigator.Group screenOptions={{ presentation: 'modal', headerBackTitleVisible: true, headerBackButtonMenuEnabled: true }}>
                <MainUserStackNavigator.Screen
                    name="SavedPlaceDetailsEdit"
                    component={SavedPlaceDetailsEditModal}
                    options={{
                        headerTitleAlign: "center",
                        headerBackButtonMenuEnabled: true,
                        headerBackVisible: true,
                        headerRight: () => (<EditHeaderRight />)
                    }}
                />
                <MainUserStackNavigator.Screen
                    name="SavedPlaceAdd"
                    component={SavedPlaceAddModal}
                    options={{
                        headerTitleAlign: "center",
                        headerBackButtonMenuEnabled: true,
                        headerBackVisible: true,
                        headerRight: () => (<EditHeaderRight />)
                    }}
                />
                <MainUserStackNavigator.Screen
                    name="UserPersonalInfoEdit"
                    component={UserPersonalInfoEditModal}
                    options={{
                        headerTitleAlign: "center",
                        headerBackButtonMenuEnabled: true,
                        headerBackVisible: true,
                        headerRight: () => (<EditHeaderRight />)
                    }}
                />
            </MainUserStackNavigator.Group>
        </MainUserStackNavigator.Navigator>
    );
}
