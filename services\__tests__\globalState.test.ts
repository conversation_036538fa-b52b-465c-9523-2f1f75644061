import { describe, expect, it } from '@jest/globals';
import { UserInfo } from '../../models/userDetails';
import useGlobalStore from '../globalState';

describe('globalState subscription/trial tests', () => {
    const currentTime = new Date('2024-01-01T12:00:00Z');

    describe('findCurrentOrLatestPastPeriod', () => {
        const findCurrentOrLatestPastPeriod = useGlobalStore.getState().findCurrentOrLatestPastPeriod;

        it('should return null when no user exists', () => {
            useGlobalStore.setState({ user: undefined });
            expect(findCurrentOrLatestPastPeriod(currentTime)).toBeNull();
        });

        it('should return active subscription when user has valid subscription', () => {
            const user: UserInfo = {
                id: 1,
                uid: 'test-uid',
                uuid: 'test-uuid',
                email: '<EMAIL>',
                createdAt: '2023-01-01T00:00:00Z',
                subscriptions: [{
                    startDate: '2023-12-01T12:00:00Z',
                    endDate: '2024-01-31T12:00:00Z',
                    paid: true,
                }]
            };

            useGlobalStore.setState({ user });
            const result = findCurrentOrLatestPastPeriod(currentTime);

            expect(result).toEqual({
                startDate: new Date('2023-12-01T12:00:00Z'),
                endDate: new Date('2024-01-31T12:00:00Z'),
                type: 'SUBSCRIPTION',
                isPaid: true
            });
        });

        it('should return ended subscription when subscription has expired', () => {
            const user: UserInfo = {
                id: 1,
                uid: 'test-uid',
                uuid: 'test-uuid',
                email: '<EMAIL>',
                createdAt: '2023-01-01T00:00:00Z',
                subscriptions: [{
                    startDate: '2023-11-01T12:00:00Z',
                    endDate: '2023-12-31T12:00:00Z',
                    paid: true,
                }]
            };

            useGlobalStore.setState({ user });
            const result = findCurrentOrLatestPastPeriod(currentTime);

            expect(result).toEqual({
                startDate: new Date('2023-11-01T12:00:00Z'),
                endDate: new Date('2023-12-31T12:00:00Z'),
                type: 'SUBSCRIPTION',
                isPaid: true
            });
        });

        it('should return active trial when user has valid trial period', () => {
            const user: UserInfo = {
                id: 1,
                uid: 'test-uid',
                uuid: 'test-uuid',
                email: '<EMAIL>',
                createdAt: '2023-01-01T00:00:00Z',
                trialStartDate: '2023-12-25T12:00:00Z',
                trialPeriodInDays: 14,
                subscriptions: []
            };

            useGlobalStore.setState({ user });
            const result = findCurrentOrLatestPastPeriod(currentTime);

            expect(result).toEqual({
                startDate: new Date('2023-12-25T12:00:00Z'),
                endDate: new Date('2024-01-08T12:00:00Z'),
                type: 'TRIAL',
            });
        });

        it('should return ended trial when trial has expired', () => {
            const user: UserInfo = {
                id: 1,
                uid: 'test-uid',
                uuid: 'test-uuid',
                email: '<EMAIL>',
                createdAt: '2023-01-01T00:00:00Z',
                trialStartDate: '2023-12-15T12:00:00Z',
                trialPeriodInDays: 14,
                subscriptions: []
            };

            useGlobalStore.setState({ user });
            const result = findCurrentOrLatestPastPeriod(currentTime);

            expect(result).toEqual({
                startDate: new Date('2023-12-15T12:00:00Z'),
                endDate: new Date('2023-12-29T12:00:00Z'),
                type: 'TRIAL',
            });
        });

        it('should prioritize subscription over trial', () => {
            const user: UserInfo = {
                id: 1,
                uid: 'test-uid',
                uuid: 'test-uuid',
                email: '<EMAIL>',
                createdAt: '2023-01-01T00:00:00Z',
                trialStartDate: '2023-12-25T12:00:00Z',
                trialPeriodInDays: 14,
                subscriptions: [{
                    startDate: '2023-12-01T12:00:00Z',
                    endDate: '2024-01-31T12:00:00Z',
                    paid: true,
                }]
            };

            useGlobalStore.setState({ user });
            const result = findCurrentOrLatestPastPeriod(currentTime);

            expect(result).toEqual({
                startDate: new Date('2023-12-01T12:00:00Z'),
                endDate: new Date('2024-01-31T12:00:00Z'),
                type: 'SUBSCRIPTION',
                isPaid: true
            });
        });

        it('should return latest past subscription when multiple non-overlapping expired subscriptions exist', () => {
            const user: UserInfo = {
                id: 1,
                uid: 'test-uid',
                uuid: 'test-uuid',
                email: '<EMAIL>',
                createdAt: '2023-01-01T00:00:00Z',
                subscriptions: [
                    {
                        startDate: '2023-09-01T12:00:00Z',
                        endDate: '2023-10-31T12:00:00Z',
                        paid: true,
                    },
                    {
                        startDate: '2023-11-01T12:00:00Z',
                        endDate: '2023-12-31T12:00:00Z',
                        paid: true,
                    }
                ]
            };

            useGlobalStore.setState({ user });
            const result = findCurrentOrLatestPastPeriod(currentTime);

            expect(result).toEqual({
                startDate: new Date('2023-11-01T12:00:00Z'),
                endDate: new Date('2023-12-31T12:00:00Z'),
                type: 'SUBSCRIPTION',
                isPaid: true
            });
        });

        it('should handle multiple non-overlapping subscriptions with one active', () => {
            const user: UserInfo = {
                id: 1,
                uid: 'test-uid',
                uuid: 'test-uuid',
                email: '<EMAIL>',
                createdAt: '2023-01-01T00:00:00Z',
                subscriptions: [
                    {
                        startDate: '2023-09-01T12:00:00Z',
                        endDate: '2023-10-31T12:00:00Z',
                        paid: true,
                    },
                    {
                        startDate: '2023-11-01T12:00:00Z',
                        endDate: '2023-12-31T12:00:00Z',
                        paid: true,
                    },
                    {
                        startDate: '2024-01-01T00:00:00Z',
                        endDate: '2024-02-29T23:59:59Z',
                        paid: true,
                    }
                ]
            };

            useGlobalStore.setState({ user });
            const result = findCurrentOrLatestPastPeriod(currentTime);

            expect(result).toEqual({
                startDate: new Date('2024-01-01T00:00:00Z'),
                endDate: new Date('2024-02-29T23:59:59Z'),
                type: 'SUBSCRIPTION',
                isPaid: true
            });
        });
    });
});


