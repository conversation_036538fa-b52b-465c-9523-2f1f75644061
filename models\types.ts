export interface TimeRemaining {
    days: number | null;
    hours: number | null;
    minutes: number | null;
    seconds: number | null;
}

export interface SubscriptionStatusData {
    endDate: Date;
    timeRemaining: TimeRemaining;
    isActive: boolean;
}

export interface SubscriptionData extends SubscriptionStatusData {
    isPaid: boolean;
}

export interface TrialData extends SubscriptionStatusData { }

export interface SubscriptionPeriod {
    readonly startDate: Date;
    readonly endDate: Date;
    readonly type: 'SUBSCRIPTION' | 'TRIAL';
    readonly plan?: string;
    readonly isPaid?: boolean; // Only applicable for SUBSCRIPTION type
}




