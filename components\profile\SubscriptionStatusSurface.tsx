import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import React, { useContext } from 'react';
import { StyleSheet, View } from 'react-native';
import { SubscriptionPeriod, TimeRemaining } from '../../models/types';
import { translationServiceContext } from '../../services/provider';
import { TranslationService } from '../../services/translationService';
import { calculateTimeRemaining, isSubscriptionActive } from '../../utils/subscriptionUtils';
import SubscribeButton from './SubscribeButton';
import SurfaceDetails from './SurfaceDetails';

interface Props {
    readonly currentOrLatestPeriod: SubscriptionPeriod;
    readonly subscribeButtonIcon: string;
    readonly currentTime: Date;
    readonly onSubscribe: () => void;
};

const SubscriptionStatusSurface: React.FC<Props> = ({
    currentOrLatestPeriod,
    subscribeButtonIcon,
    currentTime,
    onSubscribe
}) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const isActive: boolean = isSubscriptionActive(currentOrLatestPeriod, currentTime);
    const isTrial: boolean = currentOrLatestPeriod.type === 'TRIAL';
    const subscriptionPaid: boolean = currentOrLatestPeriod.type === 'SUBSCRIPTION' ? currentOrLatestPeriod.isPaid ?? false : false;
    const timeRemaining: TimeRemaining = calculateTimeRemaining(currentOrLatestPeriod.endDate, currentTime);

    const formatTimeRemaining = (remaining: TimeRemaining): string => {
        const { days, hours, minutes, seconds } = remaining;

        if ((days === null || days <= 0) && (hours === null || hours <= 0) &&
            (minutes === null || minutes <= 0) && (seconds === null || seconds <= 0)) {
            return translationService.translate(isTrial ? "YOUR_TRIAL_PERIOD_HAS_ENDED" : "YOUR_SUBSCRIPTION_HAS_ENDED");
        }

        if (days !== null && days > 1) {
            return translationService.translate(isTrial ? "TRIAL_REMAINING_DAYS" : "SUBSCRIPTION_REMAINING_DAYS")
                .replace("{days}", days.toString());
        }

        if (days === 1 && hours !== null && hours >= 24) {
            return translationService.translate(isTrial ? "TRIAL_LAST_DAY" : "SUBSCRIPTION_LAST_DAY");
        }

        if (hours !== null && hours > 0) {
            return translationService.translate(isTrial ? "TRIAL_REMAINING_HOURS" : "SUBSCRIPTION_REMAINING_HOURS")
                .replace("{hours}", hours.toString());
        }

        if (minutes !== null && minutes > 0) {
            return translationService.translate(isTrial ? "TRIAL_REMAINING_MINUTES" : "SUBSCRIPTION_REMAINING_MINUTES")
                .replace("{minutes}", minutes.toString());
        }

        if (seconds !== null && seconds > 0) {
            return translationService.translate(isTrial ? "TRIAL_REMAINING_SECONDS" : "SUBSCRIPTION_REMAINING_SECONDS")
                .replace("{seconds}", seconds.toString());
        }

        return translationService.translate(isTrial ? "YOUR_TRIAL_PERIOD_HAS_ENDED" : "YOUR_SUBSCRIPTION_HAS_ENDED");
    };

    const formatDate = (date: Date): string => {
        return format(date, 'Pp', { locale: fr });
    };

    return (
        <SurfaceDetails
            title={isActive
                ? formatTimeRemaining(timeRemaining)
                : translationService.translate(isTrial ? "YOUR_TRIAL_PERIOD_HAS_ENDED" : "YOUR_SUBSCRIPTION_HAS_ENDED")
            }
            titleColor={isActive
                ? isTrial ? "#FFA000" : undefined
                : "#D32F2F"
            }
            subtitle={isActive
                ? `${translationService.translate("END_DATE")}: ${formatDate(currentOrLatestPeriod.endDate)}`
                : translationService.translate(isTrial ? "SUBSCRIBE_TO_START" : "SUBSCRIBE_TO_RENEW")
            }
            action={isActive && currentOrLatestPeriod.type === 'SUBSCRIPTION'
                ? undefined
                : (
                    <View style={styles.actionContainer}>
                        <SubscribeButton
                            icon={subscribeButtonIcon}
                            onPress={onSubscribe}
                            style={[styles.mainButtonMarginTop, styles.subscribeButtonSelfAlign]}
                        />
                    </View>
                )
            }
        />
    );
};

const styles = StyleSheet.create({
    mainButtonMarginTop: {
        marginTop: 8,
    },
    actionContainer: {
        alignItems: 'center',
    },
    subscribeButtonSelfAlign: {
        alignSelf: 'center',
    },
    helpLinkContainer: {
        marginTop: 12,
        alignItems: 'center',
        justifyContent: 'center',
    },
    helpLink: {
        textDecorationLine: 'underline',
        color: '#1E90FF'
    }
});

export default SubscriptionStatusSurface;





