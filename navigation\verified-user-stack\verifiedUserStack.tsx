import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { User } from 'firebase/auth';
import React from 'react';
import EmailVerificationScreen from '../../screens/auth/EmailVerificationScreen';
import MainUserStack from './mainUserStack';

const VerifiedUserStackNavigator = createNativeStackNavigator<VerifiedUserStackParamList>();

interface Props {
    readonly firebaseUser: User | null;
}

export default function VerifiedUserStack(props: Props) {
    const firebaseUser: User | null = props.firebaseUser;

    const requiresVerification =
        firebaseUser && !firebaseUser.isAnonymous && !firebaseUser.emailVerified;
    console.log(requiresVerification)

    return (
        <VerifiedUserStackNavigator.Navigator screenOptions={{ headerShown: false }}>
            {requiresVerification ? (
                <VerifiedUserStackNavigator.Screen
                    name="EmailVerification"
                    component={EmailVerificationScreen}
                />
            ) : (
                <VerifiedUserStackNavigator.Screen
                    name="MainUserStack"
                    component={MainUserStack}
                />
            )}
        </VerifiedUserStackNavigator.Navigator>
    );
}
