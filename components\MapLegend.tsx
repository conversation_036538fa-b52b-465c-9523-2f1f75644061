import React from 'react';
import { StyleSheet, View } from "react-native";
import { Surface, Text, useTheme } from 'react-native-paper';

interface Props {
    readonly circleDiameter: number;
}

const MapLegend: React.FC<Props> = ({ circleDiameter }) => {

    const theme = useTheme();

    return (
        <Surface style={[styles.legendContainer, { borderRadius: theme.roundness * 2 }]}>
            <View style={styles.circleExample} />
            <Text style={styles.legendText}>{`${circleDiameter}m de rayon`}</Text>
        </Surface>
    );
};

const styles = StyleSheet.create({
    legendContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(255, 255, 255, 0.8)',
        padding: 6,
    },
    circleExample: {
        width: 20,
        height: 20,
        borderRadius: 10,
        backgroundColor: 'rgba(128, 128, 128, 0.3)', // Neutral color for circle fill
        borderWidth: 1,
        borderColor: 'rgba(128, 128, 128, 0.8)', // Neutral color for circle stroke
        marginRight: 6,
    },
    legendText: {
        fontSize: 12,
        color: 'black',
    },
});

export default MapLegend;
