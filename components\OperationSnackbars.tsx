import React from 'react';
import { Snackbar } from "react-native-paper";
import useGlobalStore from '../services/globalState';

const OperationSnackbars = () => {

    const operations = useGlobalStore((state) => state.operations);
    const removeOperation = useGlobalStore((state) => state.removeOperation);

    return (
        <>
            {operations.map((operation) => (
                <Snackbar
                    key={operation.operationId}
                    visible={operation.inProgress}
                    onDismiss={() => removeOperation(operation.operationId)}
                >
                    {operation.message}
                </Snackbar>
            ))}
        </>
    );
};

export default OperationSnackbars;
