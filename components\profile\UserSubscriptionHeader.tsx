import React, { useEffect, useState } from "react";
import { StyleSheet } from 'react-native';
import { SubscriptionPeriod, TimeRemaining } from '../../models/types';
import { UserInfo } from '../../models/userDetails';
import useGlobalStore from '../../services/globalState';
import { TIME_CONSTANTS } from "../../utils/constants";
import { calculateTimeRemaining } from '../../utils/subscriptionUtils';
import SubscriptionStatusSurface from './SubscriptionStatusSurface';
import UserHeader from './UserHeader';

interface Props {
    readonly user: UserInfo;
    readonly subscribeButtonIcon: string;
    readonly onSubscribe: () => void;
};

const shouldUpdateEveryMinute = (timeRemaining: TimeRemaining): boolean => {
    return (timeRemaining.days ?? 0) > 0 ||
        (timeRemaining.hours ?? 0) > 0 ||
        (timeRemaining.minutes ?? 0) > 2;
};

const UserSubscriptionHeader: React.FC<Props> = ({ onSubscribe, user, subscribeButtonIcon }): JSX.Element => {
    const findCurrentOrLatestPastPeriod = useGlobalStore(store => store.findCurrentOrLatestPastPeriod);
    const isUserSubscribed = useGlobalStore(store => store.isUserSubscribed);
    const userInfo: UserInfo | undefined = useGlobalStore(store => store.user);
    
    const [currentTime, setCurrentTime] = useState<Date>(new Date());

    const userCurrentOrLatestPastSubscriptionPeriod = findCurrentOrLatestPastPeriod(currentTime);
    
    useEffect(() => {
        setCurrentTime(new Date());
    }, [userInfo]);


    useEffect(() => {
        if (!userCurrentOrLatestPastSubscriptionPeriod) return;

        const timeRemaining: TimeRemaining = calculateTimeRemaining(userCurrentOrLatestPastSubscriptionPeriod.endDate, currentTime);
        const intervalMs: number = shouldUpdateEveryMinute(timeRemaining) ? TIME_CONSTANTS.MINUTE_IN_MS : TIME_CONSTANTS.SECOND_IN_MS;

        const timer = setInterval((): void => setCurrentTime(new Date()), intervalMs);
        return (): void => clearInterval(timer);
    }, [currentTime, userCurrentOrLatestPastSubscriptionPeriod]);

    return (
        <>
            <UserHeader
                user={user}
                subscribed={isUserSubscribed(currentTime)}
            />
            {userCurrentOrLatestPastSubscriptionPeriod && (
                <SubscriptionStatusSurface
                    currentOrLatestPeriod={userCurrentOrLatestPastSubscriptionPeriod}
                    currentTime={currentTime}
                    subscribeButtonIcon={subscribeButtonIcon}
                    onSubscribe={onSubscribe}
                />
            )}
        </>
    );
};

const styles = StyleSheet.create({
});

export default UserSubscriptionHeader;
