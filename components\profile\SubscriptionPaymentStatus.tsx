import React, { useContext } from 'react';
import { StyleSheet, View } from 'react-native';
import { Icon, Text } from 'react-native-paper';
import { translationServiceContext } from '../../services/provider';
import { TranslationService } from '../../services/translationService';

interface Props {
    readonly paid: boolean;
}

const SubscriptionPaymentStatus: React.FC<Props> = ({ paid }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    return (
        <View style={styles.statusContainer}>
            <Icon
                size={23}
                source={paid ? "check-circle" : "close-circle-outline"}
                color={paid ? "green" : "orange"}
            />
            <Text variant='titleMedium' style={styles.status}>
                {paid ? translationService.translate("PAID") : translationService.translate("PENDING_PAYMENT")}
            </Text>
        </View>
    );
};

const styles = StyleSheet.create({
    statusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    status: {
        marginLeft: 8,
    }
});

export default SubscriptionPaymentStatus;
