import { createNativeStackNavigator as createStackNavigator } from '@react-navigation/native-stack';
import React, { useContext } from 'react';
import AccountScreen from '../../../../screens/user/account/AccountScreen';
import { translationServiceContext } from '../../../../services/provider';
import { TranslationService } from '../../../../services/translationService';

const AccountStackNavigator = createStackNavigator<AccountStackParamList>();

function AccountStack() {
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const accountTabLabel: string = translationService.translate("TABS_ACCOUNT");

    return (
        <AccountStackNavigator.Navigator>
            <AccountStackNavigator.Screen
                name="Account"
                component={AccountScreen}
                options={{
                    title: accountTabLabel,
                    headerShown: true
                }}
            />
        </AccountStackNavigator.Navigator>
    )
};

export default AccountStack;