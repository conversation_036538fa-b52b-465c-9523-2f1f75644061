import React, { useContext } from 'react';
import { StyleSheet, View } from "react-native";
import { FAB } from 'react-native-paper';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';

interface Props {
    readonly visible: boolean;
    readonly loading: boolean;
    readonly containerStyle?: any;
    readonly onPress: () => void;
}

const SearchThisAreaFAB: React.FC<Props> = ({ visible, loading, onPress, containerStyle }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const searchThisAreaLabel: string = translationService.translate("SEARCH_THIS_AREA");

    const onFABPress = () => {
        if (!loading) {
            onPress();
        }
    };

    return (
        <View style={containerStyle}>
            <FAB
                visible={visible}
                onPress={onFABPress}
                label={searchThisAreaLabel}
                customSize={40}
                icon="map-search-outline"
                variant="surface"
                loading={loading}
            />
        </View>
    );
};

const styles = StyleSheet.create({

});

export default SearchThisAreaFAB;
