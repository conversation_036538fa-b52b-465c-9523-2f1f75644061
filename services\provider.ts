import React from 'react';
import { ApiService } from "./api/apiService";
import { TranslationService } from './translationService';
import { YupFormsService } from './yupFormsService';
import { WhatsappService } from './whatsappService';
import { WHATSAPP_NUMBER } from '../utils/constants';
import { RevenueCatService } from './revenueCatService';

const revenueCatService: RevenueCatService = new RevenueCatService();
const apiService: ApiService = new ApiService();
const translationService: TranslationService = new TranslationService();
const formsService: YupFormsService = new YupFormsService(translationService);
const whatsappService: WhatsappService = new WhatsappService(WHATSAPP_NUMBER);

export const revenueCatServiceContext = React.createContext<RevenueCatService>(revenueCatService);
export const apiServiceContext = React.createContext<ApiService>(apiService);
export const translationServiceContext = React.createContext<TranslationService>(translationService);
export const formsServiceContext = React.createContext<YupFormsService>(formsService);
export const whatsappServiceContext = React.createContext<WhatsappService>(whatsappService);